const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createTestData() {
  try {
    console.log('=== CREATING TEST DATA ===');

    // Get the test user
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: { branch: { include: { region: true } } }
    });

    if (!testUser) {
      console.log('❌ Test user not found! Please create user <NAME_EMAIL>');
      return;
    }

    console.log(`✅ Found test user: ${testUser.name} (${testUser.email})`);
    console.log(`   Branch: ${testUser.branch?.name || 'No branch'}`);
    console.log(`   Region: ${testUser.branch?.region?.name || 'No region'}`);

    // 1. CREATE CONVERTED LEADS TEST DATA
    console.log('\n=== CREATING CONVERTED LEADS TEST DATA ===');

    // Create some leads with account numbers (converted)
    const convertedLeadsData = [
      {
        customer_name: '<PERSON> Converted',
        phone_number: '+************',
        account_number: 'ACC001',
        account_number_assigned_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
        account_number_assigned_by: testUser.id,
        branch_id: testUser.branch_id,
        rm_user_id: testUser.id,
        created_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 2 weeks ago
      },
      {
        customer_name: 'Jane Smith Converted',
        phone_number: '+************',
        account_number: 'ACC002',
        account_number_assigned_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        account_number_assigned_by: testUser.id,
        branch_id: testUser.branch_id,
        rm_user_id: testUser.id,
        created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
      },
      {
        customer_name: 'Bob Johnson Converted',
        phone_number: '+************',
        account_number: 'ACC003',
        account_number_assigned_at: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000), // 25 days ago (last month)
        account_number_assigned_by: testUser.id,
        branch_id: testUser.branch_id,
        rm_user_id: testUser.id,
        created_at: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000), // 35 days ago
      }
    ];

    for (const leadData of convertedLeadsData) {
      try {
        const lead = await prisma.lead.create({
          data: leadData
        });
        console.log(`✅ Created converted lead: ${lead.customer_name} (${lead.account_number})`);
      } catch (error) {
        if (error.code === 'P2002') {
          console.log(`⚠️  Lead already exists: ${leadData.customer_name}`);
        } else {
          console.error(`❌ Error creating lead ${leadData.customer_name}:`, error.message);
        }
      }
    }

    // 2. CREATE 2BY2BY2 OVERDUE TEST DATA
    console.log('\n=== CREATING 2BY2BY2 OVERDUE TEST DATA ===');

    // First, create a customer service hitlist if needed
    let hitlist = await prisma.customerServiceHitlist.findFirst();
    if (!hitlist) {
      hitlist = await prisma.customerServiceHitlist.create({
        data: {
          name: 'Test Hitlist',
          uploader_id: testUser.id,
          file_path: '/test/path',
          total_records: 2,
          processed_records: 2,
          status: 'COMPLETED'
        }
      });
      console.log(`✅ Created customer service hitlist: ${hitlist.name}`);
    }

    // Get branch details for codes
    const branch = await prisma.branch.findUnique({
      where: { id: testUser.branch_id }
    });

    // Create branch code from name
    const branchCode = branch.name.replace(/\s+/g, '').substring(0, 5).toUpperCase();

    // Create some customer service hitlist records
    const hitlistRecords = [
      {
        customer_name: 'Overdue Customer 1',
        account_number: 'OVR001',
        phone_number: '+************',
        rm_code: testUser.rm_code,
        branch_code: branchCode,
        customer_service_hitlist_id: hitlist.id,
        created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
      },
      {
        customer_name: 'Overdue Customer 2',
        account_number: 'OVR002',
        phone_number: '+************',
        rm_code: testUser.rm_code,
        branch_code: branchCode,
        customer_service_hitlist_id: hitlist.id,
        created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
      }
    ];

    const createdHitlistRecords = [];
    for (const record of hitlistRecords) {
      try {
        const hitlist = await prisma.customerServiceHitlistRecord.create({
          data: record
        });
        createdHitlistRecords.push(hitlist);
        console.log(`✅ Created hitlist record: ${hitlist.customer_name} (${hitlist.account_number})`);
      } catch (error) {
        if (error.code === 'P2002') {
          console.log(`⚠️  Hitlist record already exists: ${record.customer_name}`);
          // Try to find existing record
          const existing = await prisma.customerServiceHitlistRecord.findFirst({
            where: { account_number: record.account_number }
          });
          if (existing) createdHitlistRecords.push(existing);
        } else {
          console.error(`❌ Error creating hitlist record ${record.customer_name}:`, error.message);
        }
      }
    }

    // Create overdue 2by2by2 phases
    const phaseTypes = ['first2', 'second2', 'third2'];
    for (const hitlist of createdHitlistRecords) {
      for (let i = 0; i < phaseTypes.length; i++) {
        try {
          const phase = await prisma.twoByTwoPhase.create({
            data: {
              type: phaseTypes[i],
              execution_date: new Date(Date.now() - (i + 1) * 24 * 60 * 60 * 1000), // 1, 2, 3 days ago
              is_completed: false,
              status: 'PENDING',
              customer_service_hitlist_record_id: hitlist.id,
              assigned_to_user_id: testUser.id,
              created_at: new Date(Date.now() - (i + 5) * 24 * 60 * 60 * 1000),
            }
          });
          console.log(`✅ Created overdue 2by2by2 phase: ${phase.type} for ${hitlist.customer_name} (due ${phase.execution_date.toDateString()})`);
        } catch (error) {
          if (error.code === 'P2002') {
            console.log(`⚠️  Phase already exists: ${phaseTypes[i]} for ${hitlist.customer_name}`);
          } else {
            console.error(`❌ Error creating phase:`, error.message);
          }
        }
      }
    }

    console.log('\n✅ Test data creation completed!');

  } catch (error) {
    console.error('Error creating test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestData();
