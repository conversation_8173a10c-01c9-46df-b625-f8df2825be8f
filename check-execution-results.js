const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkExecutionResults() {
  try {
    console.log('=== RECENT EXECUTION RESULTS ===');
    
    const targetTypes = [
      'monthly-converted-leads-reports',
      'weekly-converted-leads-reports', 
      'overdue-2by2by2-notifications',
      'overdue-2by2by2-reports'
    ];
    
    for (const type of targetTypes) {
      console.log(`\n=== ${type.toUpperCase()} ===`);
      
      const executions = await prisma.scheduledTaskExecution.findMany({
        include: {
          task: {
            select: { type: true, name: true }
          }
        },
        where: {
          task: {
            type: type
          }
        },
        orderBy: { started_at: 'desc' },
        take: 3
      });
      
      if (executions.length === 0) {
        console.log('No executions found');
        continue;
      }
      
      executions.forEach((exec, index) => {
        console.log(`\nExecution ${index + 1}:`);
        console.log(`  Status: ${exec.status}`);
        console.log(`  Started: ${exec.started_at}`);
        console.log(`  Completed: ${exec.completed_at}`);
        console.log(`  Duration: ${exec.duration_ms}ms`);
        console.log(`  Worker: ${exec.worker_id}`);
        
        if (exec.result) {
          console.log(`  Result:`, JSON.stringify(exec.result, null, 2));
        }
        
        if (exec.error_message) {
          console.log(`  Error: ${exec.error_message}`);
        }
      });
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkExecutionResults();
