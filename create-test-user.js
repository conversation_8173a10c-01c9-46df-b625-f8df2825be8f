const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const prisma = new PrismaClient();

async function createTestUser() {
  try {
    console.log('=== CHECKING EXISTING DATA ===');

    // Check existing users
    const users = await prisma.user.findMany({
      take: 5,
      include: { branch: { include: { region: true } }, role: true }
    });

    console.log(`Found ${users.length} existing users:`);
    users.forEach(user => {
      console.log(`  - ${user.name} (${user.email}) - Branch: ${user.branch?.name || 'None'} - Role: ${user.role?.name || 'None'}`);
    });

    // Check branches and regions
    const branches = await prisma.branch.findMany({
      include: { region: true },
      take: 5
    });

    console.log(`\nFound ${branches.length} branches:`);
    branches.forEach(branch => {
      console.log(`  - ${branch.name} (Region: ${branch.region?.name || 'None'})`);
    });

    // Check roles
    const roles = await prisma.role.findMany({ take: 5 });
    console.log(`\nFound ${roles.length} roles:`);
    roles.forEach(role => {
      console.log(`  - ${role.name}`);
    });

    // Create test user if not exists
    let testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    // If not found, try the existing similar user
    if (!testUser) {
      testUser = await prisma.user.findUnique({
        where: { email: '<EMAIL>' }
      });

      if (testUser) {
        console.log(`✅ Found existing similar user: ${testUser.name} (${testUser.email})`);
        console.log('Will update <NAME_EMAIL>...');

        testUser = await prisma.user.update({
          where: { id: testUser.id },
          data: { email: '<EMAIL>' }
        });

        console.log(`✅ Updated user email to: ${testUser.email}`);
      }
    }

    if (!testUser) {
      console.log('\n=== CREATING TEST USER ===');

      // Use first available branch and role, or create defaults
      let branch = branches[0];
      let role = roles[0];

      if (!branch) {
        console.log('No branches found, creating default branch and region...');

        // Create default region
        const region = await prisma.region.create({
          data: {
            name: 'Test Region',
            code: 'TEST'
          }
        });

        // Create default branch
        branch = await prisma.branch.create({
          data: {
            name: 'Test Branch',
            code: 'TEST',
            region_id: region.id
          }
        });

        console.log(`✅ Created region: ${region.name} and branch: ${branch.name}`);
      }

      if (!role) {
        console.log('No roles found, creating default role...');
        role = await prisma.role.create({
          data: {
            name: 'Test Role',
            description: 'Test role for testing purposes'
          }
        });
        console.log(`✅ Created role: ${role.name}`);
      }

      // Hash password
      const hashedPassword = await bcrypt.hash('password123', 10);

      // Create test user
      testUser = await prisma.user.create({
        data: {
          name: 'Samuel Maiko',
          email: '<EMAIL>',
          password: hashedPassword,
          branch_id: branch.id,
          role_id: role.id,
          rm_code: 'TEST001'
        }
      });

      console.log(`✅ Created test user: ${testUser.name} (${testUser.email})`);
    } else {
      console.log(`✅ Test user already exists: ${testUser.name} (${testUser.email})`);
    }

    // Get user with relations
    const fullUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        branch: { include: { region: true } },
        role: { include: { role_permissions: { include: { permission: true } } } }
      }
    });

    console.log('\n=== TEST USER DETAILS ===');
    console.log(`Name: ${fullUser.name}`);
    console.log(`Email: ${fullUser.email}`);
    console.log(`Branch: ${fullUser.branch?.name || 'None'}`);
    console.log(`Region: ${fullUser.branch?.region?.name || 'None'}`);
    console.log(`Role: ${fullUser.role?.name || 'None'}`);
    console.log(`Permissions: ${fullUser.role?.role_permissions?.length || 0}`);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
