const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkAllTasks() {
  try {
    console.log('=== ALL SCHEDULED TASKS ===');
    const tasks = await prisma.scheduledTask.findMany({
      orderBy: { created_at: 'desc' },
      take: 20
    });
    
    console.log(`Found ${tasks.length} tasks total`);
    tasks.forEach(task => {
      console.log(`Task: ${task.id} | Type: ${task.type} | Name: ${task.name} | Status: ${task.status} | Attempts: ${task.attempts}/${task.max_attempts} | Next Run: ${task.next_run_at}`);
    });
    
    console.log('\n=== LOOKING FOR SPECIFIC TASK TYPES ===');
    const targetTypes = [
      'monthly-converted-leads-reports',
      'weekly-converted-leads-reports', 
      'overdue-2by2by2-notifications',
      'overdue-2by2by2-reports'
    ];
    
    for (const type of targetTypes) {
      const task = await prisma.scheduledTask.findFirst({
        where: { type },
        orderBy: { created_at: 'desc' }
      });
      
      if (task) {
        console.log(`✅ ${type}: Found - Status: ${task.status}, Next Run: ${task.next_run_at}`);
      } else {
        console.log(`❌ ${type}: NOT FOUND - needs to be created`);
      }
    }
    
    console.log('\n=== RECENT EXECUTIONS FOR TARGET TYPES ===');
    const executions = await prisma.scheduledTaskExecution.findMany({
      include: {
        task: {
          select: { type: true, name: true }
        }
      },
      where: {
        task: {
          type: {
            in: targetTypes
          }
        }
      },
      orderBy: { started_at: 'desc' },
      take: 10
    });
    
    if (executions.length > 0) {
      executions.forEach(exec => {
        console.log(`Execution: ${exec.task.type} | Status: ${exec.status} | Started: ${exec.started_at} | Completed: ${exec.completed_at} | Error: ${exec.error_message || 'None'}`);
      });
    } else {
      console.log('No executions found for target task types');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAllTasks();
