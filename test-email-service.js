const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('./dist/src/app.module');

async function testEmailService() {
  try {
    console.log('=== TESTING EMAIL SERVICE DIRECTLY ===');
    
    // Create NestJS application
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Get email service
    const emailService = app.get('EmailService');
    
    console.log('✅ Email service obtained');
    
    // Test simple email first
    console.log('Testing simple email...');
    await emailService.sendEmail(
      '<EMAIL>',
      'Test Email from KB Tracker',
      'generic-email',
      {
        title: 'Test Email',
        recipientName: '<PERSON>',
        message: 'This is a test email to verify the email service is working.',
        appName: 'KB Tracker',
        timestamp: new Date().toLocaleString(),
      }
    );
    
    console.log('✅ Simple email sent successfully');
    
    // Test email with attachment
    console.log('Testing email with PDF attachment...');
    
    // Create a simple PDF buffer (dummy data)
    const dummyPdfBuffer = Buffer.from('PDF dummy content for testing');
    
    await emailService.sendEmailWithAttachments(
      '<EMAIL>',
      'Test Email with PDF Attachment',
      'generic-email',
      {
        title: 'Test Email with PDF',
        recipientName: 'Samuel Maiko',
        message: 'This is a test email with a PDF attachment to verify the email service is working.',
        appName: 'KB Tracker',
        timestamp: new Date().toLocaleString(),
      },
      [
        {
          filename: 'test-report.pdf',
          content: dummyPdfBuffer,
          contentType: 'application/pdf',
        },
      ]
    );
    
    console.log('✅ Email with attachment sent successfully');
    
    await app.close();
    
  } catch (error) {
    console.error('❌ Email service test failed:', error);
    console.error('Error details:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

testEmailService();
