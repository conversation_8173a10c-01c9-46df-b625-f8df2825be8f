const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function triggerTestTasks() {
  try {
    console.log('=== CREATING TEST TASKS FOR IMMEDIATE EXECUTION ===');
    
    const testTasks = [
      {
        type: 'monthly-converted-leads-reports',
        name: 'TEST - Monthly Converted Leads Reports',
        description: 'Test execution of monthly converted leads reports',
        payload: {},
        run_at: new Date(Date.now() + 5000), // 5 seconds from now
        priority: 10,
        max_attempts: 1
      },
      {
        type: 'weekly-converted-leads-reports',
        name: 'TEST - Weekly Converted Leads Reports', 
        description: 'Test execution of weekly converted leads reports',
        payload: {},
        run_at: new Date(Date.now() + 10000), // 10 seconds from now
        priority: 10,
        max_attempts: 1
      },
      {
        type: 'overdue-2by2by2-notifications',
        name: 'TEST - Overdue 2by2by2 Notifications',
        description: 'Test execution of overdue 2by2by2 notifications',
        payload: {},
        run_at: new Date(Date.now() + 15000), // 15 seconds from now
        priority: 10,
        max_attempts: 1
      },
      {
        type: 'overdue-2by2by2-reports',
        name: 'TEST - Overdue 2by2by2 Reports',
        description: 'Test execution of overdue 2by2by2 reports',
        payload: {},
        run_at: new Date(Date.now() + 20000), // 20 seconds from now
        priority: 10,
        max_attempts: 1
      }
    ];
    
    for (const taskData of testTasks) {
      try {
        const task = await prisma.scheduledTask.create({
          data: taskData
        });
        console.log(`✅ Created test task: ${task.name} (${task.type}) - will run at ${task.run_at.toLocaleTimeString()}`);
      } catch (error) {
        console.error(`❌ Error creating task ${taskData.name}:`, error.message);
      }
    }
    
    console.log('\n✅ All test tasks created! They should execute within the next 30 seconds.');
    console.log('Watch the worker logs to see the execution results.');
    
    // Wait a bit and then check results
    console.log('\n⏳ Waiting 35 seconds to check results...');
    await new Promise(resolve => setTimeout(resolve, 35000));
    
    console.log('\n=== CHECKING EXECUTION RESULTS ===');
    
    for (const taskData of testTasks) {
      const executions = await prisma.scheduledTaskExecution.findMany({
        include: {
          task: {
            select: { type: true, name: true }
          }
        },
        where: {
          task: {
            type: taskData.type,
            name: taskData.name
          }
        },
        orderBy: { started_at: 'desc' },
        take: 1
      });
      
      if (executions.length > 0) {
        const exec = executions[0];
        console.log(`\n${taskData.type}:`);
        console.log(`  Status: ${exec.status}`);
        console.log(`  Duration: ${exec.duration_ms}ms`);
        if (exec.result) {
          console.log(`  Result:`, JSON.stringify(exec.result, null, 2));
        }
        if (exec.error_message) {
          console.log(`  Error: ${exec.error_message}`);
        }
      } else {
        console.log(`\n${taskData.type}: No execution found`);
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

triggerTestTasks();
