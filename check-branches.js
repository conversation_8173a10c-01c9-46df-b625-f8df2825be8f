const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkBranches() {
  try {
    const branches = await prisma.branch.findMany({
      include: { region: true }
    });
    
    console.log('=== ALL BRANCHES ===');
    branches.forEach(branch => {
      console.log(`Branch: ${branch.name} | Code: ${branch.code || 'NULL'} | Region: ${branch.region?.name || 'None'}`);
    });
    
    // Update branch codes if they're null
    for (const branch of branches) {
      if (!branch.code) {
        const code = branch.name.replace(/\s+/g, '').substring(0, 5).toUpperCase();
        await prisma.branch.update({
          where: { id: branch.id },
          data: { code: code }
        });
        console.log(`✅ Updated branch ${branch.name} with code: ${code}`);
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkBranches();
