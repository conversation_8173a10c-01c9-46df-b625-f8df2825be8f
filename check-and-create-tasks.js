const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkAndCreateTasks() {
  try {
    console.log('=== CHECKING EXISTING SCHEDULED TASKS ===');
    
    const taskTypes = [
      'monthly-converted-leads-reports',
      'weekly-converted-leads-reports', 
      'overdue-2by2by2-notifications',
      'overdue-2by2by2-reports'
    ];
    
    const existingTasks = await prisma.scheduledTask.findMany({
      where: {
        type: {
          in: taskTypes
        }
      },
      orderBy: { created_at: 'desc' }
    });
    
    console.log(`Found ${existingTasks.length} existing tasks:`);
    existingTasks.forEach(task => {
      console.log(`- ${task.type}: ${task.status} (${task.attempts}/${task.max_attempts} attempts)`);
    });
    
    // Check which tasks are missing
    const existingTypes = existingTasks.map(t => t.type);
    const missingTypes = taskTypes.filter(type => !existingTypes.includes(type));
    
    console.log('\n=== MISSING TASKS ===');
    if (missingTypes.length === 0) {
      console.log('All task types exist!');
    } else {
      console.log(`Missing: ${missingTypes.join(', ')}`);
    }
    
    // Create missing tasks for immediate testing
    const now = new Date();
    const in5Minutes = new Date(now.getTime() + 5 * 60 * 1000);
    
    for (const taskType of missingTypes) {
      console.log(`\n=== CREATING ${taskType.toUpperCase()} TASK ===`);
      
      let taskData = {
        type: taskType,
        name: getTaskName(taskType),
        description: getTaskDescription(taskType),
        payload: {},
        run_at: in5Minutes,
        priority: 5,
        max_attempts: 3,
        updated_at: now
      };
      
      // Add interval for recurring tasks
      if (taskType.includes('weekly')) {
        taskData.interval_type = 'WEEKS';
        taskData.interval_value = 1;
      } else if (taskType.includes('monthly')) {
        taskData.interval_type = 'MONTHS';
        taskData.interval_value = 1;
      } else if (taskType.includes('notifications')) {
        taskData.interval_type = 'DAYS';
        taskData.interval_value = 1;
      } else if (taskType.includes('reports')) {
        taskData.interval_type = 'DAYS';
        taskData.interval_value = 1;
      }
      
      const createdTask = await prisma.scheduledTask.create({
        data: taskData
      });
      
      console.log(`✅ Created task: ${createdTask.id}`);
      console.log(`   Type: ${createdTask.type}`);
      console.log(`   Run at: ${createdTask.run_at}`);
      console.log(`   Interval: ${createdTask.interval_type} ${createdTask.interval_value}`);
    }
    
    // Also create immediate test tasks (run in 30 seconds) for all types
    console.log('\n=== CREATING IMMEDIATE TEST TASKS ===');
    const in30Seconds = new Date(now.getTime() + 30 * 1000);
    
    for (const taskType of taskTypes) {
      const testTask = await prisma.scheduledTask.create({
        data: {
          type: taskType,
          name: `TEST - ${getTaskName(taskType)}`,
          description: `Test execution of ${getTaskDescription(taskType)}`,
          payload: { test: true },
          run_at: in30Seconds,
          priority: 10, // High priority for testing
          max_attempts: 1, // Only try once for tests
          updated_at: now
        }
      });
      
      console.log(`✅ Created test task: ${testTask.id} (${taskType}) - runs in 30 seconds`);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

function getTaskName(taskType) {
  switch (taskType) {
    case 'monthly-converted-leads-reports':
      return 'Monthly Converted Leads Reports';
    case 'weekly-converted-leads-reports':
      return 'Weekly Converted Leads Reports';
    case 'overdue-2by2by2-notifications':
      return 'Overdue 2by2by2 Notifications';
    case 'overdue-2by2by2-reports':
      return 'Overdue 2by2by2 Reports';
    default:
      return taskType;
  }
}

function getTaskDescription(taskType) {
  switch (taskType) {
    case 'monthly-converted-leads-reports':
      return 'Sends monthly converted leads reports to users with appropriate permissions';
    case 'weekly-converted-leads-reports':
      return 'Sends weekly converted leads reports to users with appropriate permissions';
    case 'overdue-2by2by2-notifications':
      return 'Sends notifications for overdue 2by2by2 activities';
    case 'overdue-2by2by2-reports':
      return 'Generates and sends PDF reports of overdue 2by2by2 activities';
    default:
      return taskType;
  }
}

checkAndCreateTasks();
