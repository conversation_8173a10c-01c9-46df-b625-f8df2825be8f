import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Res,
  Header,
  UseGuards,
  Request,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Express, Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiConflictResponse,
  ApiBearerAuth,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import { LeadsService } from './leads.service';
import { CreateLeadNewDto, BulkCreateLeadsDto, ExcelUploadResponseDto, ExcelUploadDto } from './dto/create-lead-new.dto';
import { UpdateLeadDto } from './dto/update-lead.dto';
import { LeadResponseDto } from './dto/lead-response.dto';
import { LeadSummaryResponseDto } from './dto/lead-summary-response.dto';
import { ConvertLeadDto } from './dto/convert-lead.dto';
import { ConvertLeadResponseDto } from './dto/convert-lead-response.dto';
import { LeadAttachmentsResponseDto } from './dto/lead-attachments-response.dto';
import {
  CreateLeadContactPersonDto,
  UpdateLeadContactPersonDto,
  LeadContactPersonResponseDto,
} from './dto/lead-contact-person.dto';
import { UpdateLeadRmDto } from './dto/update-lead-rm.dto';
import {
  PaginationDto,
  PaginatedResponseDto,
} from '../common/dto/pagination.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { OptionalUUIDPipe } from '../common/pipes/optional-uuid.pipe';
import { RbacAnalyticsQueryDto } from './dto/rbac-analytics-query.dto';

/**
 * Controller handling all lead-related HTTP endpoints
 * Provides RESTful API for lead management
 */
@ApiTags('Leads')
@Controller('leads')
export class LeadsController {
  constructor(private readonly leadsService: LeadsService) {}

  /**
   * Creates a new lead and returns summary data
   * POST /leads
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create a new lead',
    description:
      'Creates a new lead with flexible input format and returns summary data including activity counts and last interaction.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Lead created successfully',
    type: LeadSummaryResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({
    description:
      'Referenced entity not found (customer category, ISIC sector, branch, or employer)',
  })
  @ApiConflictResponse({ description: 'Client ID already exists' })
  async create(
    @Body(ValidationPipe) createLeadNewDto: CreateLeadNewDto,
    @Request() req: any,
  ): Promise<LeadSummaryResponseDto> {
    // Pass the entire authenticated user object instead of just the ID
    return this.leadsService.createNewFormat(createLeadNewDto, req.user);
  }

  /**
   * Creates multiple leads at once with automatic foreign key handling
   * POST /leads/bulk
   */
  @Post('bulk')
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create multiple leads at once',
    description:
      'Creates multiple leads in a single operation with automatic foreign key handling. For foreign key fields (branch, customer category, employer, ISIC sector), if the provided value is not a UUID, the system will attempt to find an existing record by name. If no record is found, a new one will be created automatically.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Leads created successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Successfully created 5 leads' },
        totalCreated: { type: 'number', example: 5 },
        createdLeads: {
          type: 'array',
          items: { $ref: '#/components/schemas/LeadSummaryResponseDto' },
        },
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              index: { type: 'number', example: 2 },
              error: { type: 'string', example: 'Client ID already exists' },
              leadData: { type: 'object' },
            },
          },
        },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({ description: 'Some leads could not be created due to conflicts' })
  async createBulk(
    @Body(ValidationPipe) bulkCreateLeadsDto: BulkCreateLeadsDto,
    @Request() req: any,
  ) {
    return this.leadsService.createBulk(bulkCreateLeadsDto, req.user);
  }

  /**
   * Creates leads from an uploaded Excel file
   * POST /leads/upload-excel
   */
  @Post('upload-excel')
  @HttpCode(HttpStatus.CREATED)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @UseInterceptors(FileInterceptor('file', {
    fileFilter: (req, file, callback) => {
      if (!file.originalname.match(/\.(xlsx|xls)$/)) {
        return callback(new BadRequestException('Only Excel files (.xlsx, .xls) are allowed'), false);
      }
      callback(null, true);
    },
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB limit
    },
  }))
  @ApiOperation({
    summary: 'Upload Excel file to create leads with anchor processing',
    description:
      'Uploads an Excel file containing lead data and creates leads in bulk. The system automatically parses the Excel file, maps columns to lead fields, and processes anchor names with fuzzy matching. If an anchor name in the Excel matches an existing anchor (60%+ similarity), it links to that anchor. Otherwise, it creates a new anchor. Supports automatic foreign key resolution and provides detailed error reporting including anchor processing results.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Excel file processed successfully',
    type: ExcelUploadResponseDto,
  })
  @ApiBadRequestResponse({
    description: 'Invalid file format, file too large, or parsing errors'
  })
  @ApiConflictResponse({
    description: 'Some leads could not be created due to conflicts'
  })
  async uploadExcel(
    @UploadedFile() file: Express.Multer.File,
    @Request() req: any,
  ): Promise<ExcelUploadResponseDto> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return this.leadsService.createFromExcel(file, req.user);
  }

  /**
   * Retrieves all leads with pagination and search
   * GET /leads
   */
  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all leads',
    description:
      'Retrieves a paginated list of leads with optional search functionality. Search works on customer name, client ID, phone number, lead type, RM name, and branch name. Includes comprehensive relationship data. Access is controlled by user permissions: view.all.leads (all leads), view.my.leads (own leads), or view.branch.leads (branch leads).',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description:
      'Search term for customer name, client ID, phone number, lead type, RM name, or branch name',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Leads retrieved successfully',
    type: PaginatedResponseDto<LeadSummaryResponseDto>,
  })
  async findAll(
    @Query(ValidationPipe) paginationDto: PaginationDto,
    @Request() req: any,
  ): Promise<PaginatedResponseDto<LeadSummaryResponseDto>> {
    return this.leadsService.findAll(paginationDto, req.user);
  }

  /**
   * Retrieves all leads without pagination
   * GET /leads/all
   */
  @Get('all')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get all leads without pagination',
    description:
      'Retrieves all leads in the system without pagination. Useful for exports, reports, or when you need the complete dataset. Supports optional search filtering to narrow down results. Access is controlled by user permissions: view.all.leads (all leads), view.my.leads (own leads), or view.branch.leads (branch leads).',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Optional search term to filter leads by customer name, client ID, phone number, lead type, RM name, or branch name',
    example: 'John Doe',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'All leads retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/LeadSummaryResponseDto' },
        },
        total: { type: 'number', example: 1250 },
        message: { type: 'string', example: 'Retrieved all 1250 leads successfully' },
      },
    },
  })
  async findAllWithoutPagination(
    @Request() req: any,
    @Query('search') search?: string,
    
  ) {
    return this.leadsService.findAllWithoutPagination(search, req.user);
  }

  /**
   * Exports all leads to Excel file
   * GET /leads/export
   */
  @Get('export')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @Header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
  @ApiOperation({
    summary: 'Export all leads to Excel file',
    description:
      'Exports all leads in the system to an Excel file (.xlsx) and sends it as a downloadable file. The file includes comprehensive lead data with all relationships and activity counts. Supports optional search filtering to export specific leads. Access is controlled by user permissions: view.all.leads (all leads), view.my.leads (own leads), or view.branch.leads (branch leads).',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Optional search term to filter leads before export',
    example: 'corporate',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Excel file generated and sent successfully',
    headers: {
      'Content-Type': {
        description: 'MIME type for Excel files',
        schema: { type: 'string', example: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
      },
      'Content-Disposition': {
        description: 'Attachment header with filename',
        schema: { type: 'string', example: 'attachment; filename="leads-export-2025-07-30.xlsx"' },
      },
    },
  })
  async exportLeads(
    @Res() res: Response,
    @Request() req: any,
    @Query('search') search?: string,
   
  ): Promise<void> {
    const buffer = await this.leadsService.exportLeadsToExcel(search, req.user);

    // Generate filename with current date
    const date = new Date().toISOString().split('T')[0];
    const filename = `leads-export-${date}.xlsx`;

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', buffer.length);

    // Send the Excel file
    res.end(buffer);
  }

  /**
   * Get RBAC-enabled lead analytics
   * GET /leads/rbac-analytics
   */
  @Get('rbac-analytics')
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequirePermissions('view.all.leads', 'view.my.leads')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get RBAC-enabled lead analytics',
    description: 'Retrieves lead analytics based on user permissions. Users with "view.all.leads" see all leads, users with "view.my.leads" see only their assigned leads. Supports filtering by branch and date range. Also includes Month-to-Date (MTD) activity data for visits and calls made by the logged-in user.',
  })
  @ApiQuery({
    name: 'branch_id',
    required: false,
    type: String,
    description: 'Filter by specific branch ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiQuery({
    name: 'start_date',
    required: false,
    type: String,
    description: 'Filter leads created from this date (ISO format)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'end_date',
    required: false,
    type: String,
    description: 'Filter leads created to this date (ISO format)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiResponse({
    status: 200,
    description: 'Lead analytics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        total_leads: { type: 'number', example: 150 },
        contacted_leads: { type: 'number', example: 85 },
        leads_by_status: {
          type: 'object',
          properties: {
            pending: { type: 'number', example: 45 },
            warm: { type: 'number', example: 30 },
            hot: { type: 'number', example: 25 },
            cold: { type: 'number', example: 50 },
          },
        },
        user_activity_mtd: {
          type: 'object',
          properties: {
            total_visits: { type: 'number', example: 12, description: 'Total visits made by the logged-in user in the current month' },
            total_calls: { type: 'number', example: 25, description: 'Total calls made by the logged-in user in the current month' },
            month_year: { type: 'string', example: '2025-08', description: 'Current month and year in YYYY-MM format' },
          },
        },
        user_permissions: {
          type: 'object',
          properties: {
            can_view_all_leads: { type: 'boolean', example: true },
            applied_filter: { type: 'string', example: 'all_leads' },
          },
        },
        filters_applied: {
          type: 'object',
          properties: {
            branch_id: { type: 'string', nullable: true },
            start_date: { type: 'string', nullable: true },
            end_date: { type: 'string', nullable: true },
          },
        },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Invalid query parameters or insufficient permissions' })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Missing or invalid authentication token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions (requires view.all.leads or view.my.leads)',
  })
  async getRbacLeadAnalytics(
    @Request() req: any,
    @Query('branch_id') branchId?: string,
    @Query('start_date') startDate?: string,
    @Query('end_date') endDate?: string,
  ) {
    try {
      // Manual validation for branch_id if provided
      if (branchId && branchId.trim() !== '') {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(branchId)) {
          throw new BadRequestException('Branch ID must be a valid UUID');
        }
      }

      return await this.leadsService.getRbacLeadAnalytics(req.user, {
        branchId: branchId && branchId.trim() !== '' ? branchId : undefined,
        startDate,
        endDate,
      });
    } catch (error) {
      // Log the error for debugging
      console.error('Error in getRbacLeadAnalytics controller:', error);
      throw error;
    }
  }

  /**
   * Get RBAC-enabled leads list
   * GET /leads/rbac-leads
   */
  @Get('rbac-leads')
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequirePermissions('view.all.leads', 'view.my.leads')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get RBAC-enabled leads list',
    description: 'Retrieves a paginated list of leads based on user permissions. Users with "view.all.leads" see all leads, users with "view.my.leads" see only their assigned leads.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10, max: 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'branch_id',
    required: false,
    type: String,
    description: 'Filter by specific branch ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Filter by lead status (pending, warm, hot, cold)',
    example: 'pending',
  })
  @ApiQuery({
    name: 'start_date',
    required: false,
    type: String,
    description: 'Filter leads created from this date (ISO format)',
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'end_date',
    required: false,
    type: String,
    description: 'Filter leads created to this date (ISO format)',
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiResponse({
    status: 200,
    description: 'Leads retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/LeadSummaryResponseDto' },
        },
        meta: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 150 },
            page: { type: 'number', example: 1 },
            limit: { type: 'number', example: 10 },
            totalPages: { type: 'number', example: 15 },
            hasNextPage: { type: 'boolean', example: true },
            hasPreviousPage: { type: 'boolean', example: false },
            user_permissions: {
              type: 'object',
              properties: {
                can_view_all_leads: { type: 'boolean', example: true },
                applied_filter: { type: 'string', example: 'all_leads' },
              },
            },
          },
        },
      },
    },
  })
  async getRbacLeads(
    @Request() req: any,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('branch_id') branchId?: string,
    @Query('status') status?: string,
    @Query('start_date') startDate?: string,
    @Query('end_date') endDate?: string,
  ) {
    // Manual validation for branch_id if provided
    if (branchId && branchId.trim() !== '') {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(branchId)) {
        throw new BadRequestException('Branch ID must be a valid UUID');
      }
    }

    const pageNumber = parseInt(page, 10);
    const limitNumber = parseInt(limit, 10);

    return this.leadsService.getRbacLeads(req.user, {
      page: pageNumber,
      limit: limitNumber,
      branchId: branchId && branchId.trim() !== '' ? branchId : undefined,
      status,
      startDate,
      endDate,
    });
  }

  /**
   * Retrieves a single lead by ID
   * GET /leads/:id
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get lead by ID',
    description:
      'Retrieves a single lead with comprehensive relationship data and activity counts. Includes all contact persons and related entity information.',
  })
  @ApiParam({ name: 'id', description: 'Lead UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lead retrieved successfully',
    type: LeadResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Lead not found' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<LeadResponseDto> {
    return this.leadsService.findOne(id);
  }

  /**
   * Updates a lead
   * PATCH /leads/:id
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update lead',
    description:
      'Updates a lead with the provided information. All fields are optional for partial updates. Foreign key relationships are validated.',
  })
  @ApiParam({ name: 'id', description: 'Lead UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lead updated successfully',
    type: LeadSummaryResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({
    description: 'Lead not found or referenced entity not found',
  })
  @ApiConflictResponse({ description: 'Client ID already exists' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateLeadDto: UpdateLeadDto,
  ): Promise<LeadSummaryResponseDto> {
    return this.leadsService.update(id, updateLeadDto);
  }

  /**
   * Updates the RM user ID of a lead
   * PATCH /leads/:id/rm
   */
  @Patch(':id/rm')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update lead RM user',
    description: 'Updates the RM user ID of a lead. Only the RM user ID can be updated through this endpoint.',
  })
  @ApiParam({ name: 'id', description: 'Lead UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lead RM user updated successfully',
    type: LeadSummaryResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({
    description: 'Lead not found or RM user not found',
  })
  async updateLeadRm(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateLeadRmDto: UpdateLeadRmDto,
  ): Promise<LeadSummaryResponseDto> {
    return this.leadsService.updateLeadRm(id, updateLeadRmDto.rmUserId);
  }

  /**
   * Deletes a lead
   * DELETE /leads/:id
   */
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete lead',
    description:
      'Deletes a lead by ID. Cannot delete leads that have associated activities, hitlist entries, scheduled visits, loan activities, or referred leads. All contact persons are also deleted.',
  })
  @ApiParam({ name: 'id', description: 'Lead UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Lead deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Lead not found' })
  @ApiConflictResponse({
    description: 'Lead has associated data and cannot be deleted',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.leadsService.remove(id);
  }

  /**
   * Retrieves contact persons for a specific lead
   * GET /leads/:id/contact-persons
   */
  @Get(':id/contact-persons')
  @ApiOperation({
    summary: 'Get lead contact persons',
    description:
      'Retrieves all contact persons for a specific lead with pagination and search functionality.',
  })
  @ApiParam({ name: 'id', description: 'Lead UUID' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10, max: 100)',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for contact person name or phone number',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Contact persons retrieved successfully',
    type: PaginatedResponseDto<LeadContactPersonResponseDto>,
  })
  @ApiNotFoundResponse({ description: 'Lead not found' })
  async getLeadContactPersons(
    @Param('id', ParseUUIDPipe) id: string,
    @Query(ValidationPipe) paginationDto: PaginationDto,
  ): Promise<PaginatedResponseDto<LeadContactPersonResponseDto>> {
    return this.leadsService.getLeadContactPersons(id, paginationDto);
  }

  /**
   * Adds a contact person to a lead
   * POST /leads/contact-persons
   */
  @Post('contact-persons')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Add contact person to lead',
    description: 'Creates a new contact person for a specific lead.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Contact person created successfully',
    type: LeadContactPersonResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Lead not found' })
  async addContactPerson(
    @Body(ValidationPipe) createContactPersonDto: CreateLeadContactPersonDto,
  ): Promise<LeadContactPersonResponseDto> {
    return this.leadsService.addContactPerson(createContactPersonDto);
  }

  /**
   * Updates a contact person
   * PATCH /leads/contact-persons/:id
   */
  @Patch('contact-persons/:id')
  @ApiOperation({
    summary: 'Update contact person',
    description:
      'Updates a contact person with the provided information. All fields are optional for partial updates.',
  })
  @ApiParam({ name: 'id', description: 'Contact person UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Contact person updated successfully',
    type: LeadContactPersonResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Contact person not found' })
  async updateContactPerson(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateContactPersonDto: UpdateLeadContactPersonDto,
  ): Promise<LeadContactPersonResponseDto> {
    return this.leadsService.updateContactPerson(id, updateContactPersonDto);
  }

  /**
   * Deletes a contact person
   * DELETE /leads/contact-persons/:id
   */
  @Delete('contact-persons/:id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete contact person',
    description: 'Deletes a contact person by ID.',
  })
  @ApiParam({ name: 'id', description: 'Contact person UUID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Contact person deleted successfully',
  })
  @ApiNotFoundResponse({ description: 'Contact person not found' })
  async removeContactPerson(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<void> {
    return this.leadsService.removeContactPerson(id);
  }

  /**
   * Converts a lead by assigning an account number and generating a client ID
   * POST /leads/:id/convert
   */
  @Post(':id/convert')
  @HttpCode(HttpStatus.OK)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Convert lead',
    description:
      'Converts a lead by assigning an account number and automatically generating a client ID in the format "CL865972". Returns the lead ID upon successful conversion.',
  })
  @ApiParam({ name: 'id', description: 'Lead UUID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lead converted successfully',
    type: ConvertLeadResponseDto,
  })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiNotFoundResponse({ description: 'Lead not found' })
  @ApiConflictResponse({ description: 'Account number already in use' })
  async convertLead(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) convertLeadDto: ConvertLeadDto,
    @Request() req: any,
  ): Promise<ConvertLeadResponseDto> {
    return this.leadsService.convertLead(id, convertLeadDto, req.user);
  }

  /**
   * Gets all attachments for a specific lead
   * GET /leads/:id/attachments
   */
  @Get(':id/attachments')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get all attachments for a lead',
    description:
      'Retrieves all file attachments associated with a specific lead from all activities (calls and visits). Each attachment includes creation timestamp and activity context.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID of the lead to get attachments for',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lead attachments retrieved successfully',
    type: LeadAttachmentsResponseDto,
  })
  @ApiNotFoundResponse({
    description: 'Lead not found',
  })
  async getLeadAttachments(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<LeadAttachmentsResponseDto> {
    return this.leadsService.getLeadAttachments(id);
  }




}
