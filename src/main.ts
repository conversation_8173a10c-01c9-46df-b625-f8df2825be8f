import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Logger } from 'nestjs-pino';
import { AppModule } from './app.module';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function syncDatabaseSchema() {
  console.log('🔄 Syncing database schema...');
  try {
    const { stdout, stderr } = await execAsync(
      'npx prisma db push --accept-data-loss',
    );
    if (stdout) console.log(stdout);
    if (stderr) console.error(stderr);
    console.log('✅ Database schema synchronized');
  } catch (error) {
    console.error('❌ Failed to sync database schema:', error.message);
    // Don't exit - let the app start anyway, scheduler will handle missing tables gracefully
    console.warn('⚠️ Continuing startup despite schema sync failure...');
  }
}

async function bootstrap() {
  // Set timezone for the entire application
  process.env.TZ = process.env.TZ || 'Africa/Nairobi';

  // Sync database schema only in production or when explicitly requested
  // This prevents repeated schema syncing on every dev restart
  if (
    process.env.NODE_ENV === 'production' ||
    process.env.FORCE_SYNC_DATABASE_SCHEMA === 'true'
  ) {
    await syncDatabaseSchema();
  }

  const app = await NestFactory.create(AppModule, { bufferLogs: true });

  // Use pino logger for all application logging
  app.useLogger(app.get(Logger));

  // Enable CORS for frontend integration
  app.enableCors({
    origin: process.env.FRONTEND_URL || 'http://localhost:5173',
    credentials: true,
  });

  // Global validation pipe with transformation
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true, // Automatically transform payloads to DTO instances
      whitelist: true, // Strip properties that don't have decorators
      forbidNonWhitelisted: true, // Throw error if non-whitelisted properties are present
      transformOptions: {
        enableImplicitConversion: true, // Enable implicit type conversion
      },
    }),
  );

  // Swagger API documentation setup
  const config = new DocumentBuilder()
    .setTitle('KB Tracker API')
    .setDescription(
      'Knowledge Base Tracker Backend API for managing regions, branches, ISIC sectors, customer categories, employers, anchor relationships, leads, and customer activities',
    )
    .setVersion('1.0')
    .addTag('Regions', 'Region management endpoints')
    .addTag('Branches', 'Branch management endpoints')
    .addTag('ISIC Sectors', 'ISIC sector management endpoints')
    .addTag('Customer Categories', 'Customer category management endpoints')
    .addTag(
      'Customer Feedback Categories',
      'Customer feedback category management endpoints',
    )
    .addTag('Employers', 'Employer management endpoints')
    .addTag('Anchor Relationships', 'Anchor relationship management endpoints')
    .addTag('Leads', 'Lead management endpoints with contact persons')
    .addTag('Activities', 'Activity management endpoints for calls and visits')
    .addTag('Purpose of Activities', 'Purpose of activity management endpoints')
    .addTag('Purpose Categories', 'Purpose category management endpoints')
    .addTag('Anchors', 'Anchor management endpoints')
    .addTag('permissions', 'Permission management endpoints')
    .addTag('roles', 'Role management endpoints')
    .addTag(
      'role-permissions',
      'Role-permission relationship management endpoints',
    )
    .addTag('users', 'User management endpoints')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
    },
  });

  // Global prefix for all routes except uploads (static file serving)
  app.setGlobalPrefix('api/v1', {
    exclude: ['uploads/*path'],
  });

  const port = process.env.PORT ?? 3000;
  await app.listen(port);

  const logger = app.get(Logger);
  logger.log(`🚀 Application is running on: http://localhost:${port}`);
  logger.log(`📚 Swagger documentation: http://localhost:${port}/api/docs`);
}

bootstrap();
