import { Injectable, Logger } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { NotificationHandler, NotificationContext } from './handlers/notification.handler.interface';
import { LeadAssignedHandler } from './handlers/lead-assigned.handler';
import { UserCreatedHandler } from './handlers/user-created.handler';
import { ActivityCreatedHandler } from './handlers/activity-created.handler';
import { DormancyHitlistAssignedHandler } from './handlers/dormancy-hitlist-assigned.handler';
import { TwoByTwoHitlistAssignedHandler } from './handlers/two-by-two-hitlist-assigned.handler';
import { NotificationType } from '@prisma/client';

@Injectable()
export class NotificationTriggerService {
  private readonly logger = new Logger(NotificationTriggerService.name);
  private readonly handlers: Map<NotificationType, NotificationHandler> = new Map();

  constructor(
    private readonly notificationsService: NotificationsService,
    private readonly leadAssignedHandler: LeadAssignedHandler,
    private readonly userCreatedHandler: User<PERSON>reated<PERSON>and<PERSON>,
    private readonly activityCreatedHandler: ActivityCreatedHandler,
    private readonly dormancyHitlistAssignedHandler: DormancyHitlistAssignedHandler,
    private readonly twoByTwoHitlistAssignedHandler: TwoByTwoHitlistAssignedHandler,
  ) {
    // Register notification handlers
    this.handlers.set(NotificationType.LEAD_ASSIGNED, this.leadAssignedHandler);
    this.handlers.set(NotificationType.USER_CREATED, this.userCreatedHandler);
    this.handlers.set(NotificationType.ACTIVITY_CREATED, this.activityCreatedHandler);
    this.handlers.set(NotificationType.DORMANCY_HITLIST_ASSIGNED, this.dormancyHitlistAssignedHandler);
    this.handlers.set(NotificationType.TWO_BY_TWO_HITLIST_ASSIGNED, this.twoByTwoHitlistAssignedHandler);
  }

  /**
   * Trigger notifications for a specific event
   */
  async triggerNotification(
    type: NotificationType,
    context: NotificationContext,
  ): Promise<void> {
    try {
      this.logger.log(`Triggering notification for type: ${type}`);

      const handler = this.handlers.get(type);
      if (!handler) {
        this.logger.warn(`No handler found for notification type: ${type}`);
        return;
      }

      if (!handler.shouldHandle(context)) {
        this.logger.log(`Handler for ${type} determined it should not handle this context`);
        return;
      }

      const notificationData = await handler.generateNotification(context);
      
      if (notificationData.recipientIds.length === 0) {
        this.logger.log(`No recipients found for notification type: ${type}`);
        return;
      }

      // Create notifications for all recipients
      const notifications = notificationData.recipientIds.map(recipientId => ({
        type,
        title: notificationData.title,
        message: notificationData.message,
        data: notificationData.data,
        recipient_id: recipientId,
        sender_id: context.triggerUserId,
        entity_type: context.entityType,
        entity_id: context.entityId,
        priority: notificationData.priority,
      }));

      await this.notificationsService.createBatch(notifications);
      
      this.logger.log(`Successfully triggered ${notifications.length} notifications for type: ${type}`);

    } catch (error) {
      this.logger.error(`Failed to trigger notification for type ${type}: ${error.message}`, error.stack);
    }
  }

  /**
   * Trigger lead assigned notification
   */
  async triggerLeadAssigned(
    leadId: string,
    assignedUserId: string,
    triggerUserId?: string,
  ): Promise<void> {
    await this.triggerNotification(NotificationType.LEAD_ASSIGNED, {
      entityType: 'lead',
      entityId: leadId,
      entityData: { assignedUserId },
      triggerUserId,
    });
  }

  /**
   * Trigger user created notification
   */
  async triggerUserCreated(
    userId: string,
    triggerUserId?: string,
  ): Promise<void> {
    await this.triggerNotification(NotificationType.USER_CREATED, {
      entityType: 'user',
      entityId: userId,
      entityData: { isNewUser: true },
      triggerUserId,
    });
  }

  /**
   * Trigger activity created notification
   */
  async triggerActivityCreated(
    activityId: string,
    triggerUserId?: string,
  ): Promise<void> {
    await this.triggerNotification(NotificationType.ACTIVITY_CREATED, {
      entityType: 'activity',
      entityId: activityId,
      entityData: { isNewActivity: true },
      triggerUserId,
    });
  }

  /**
   * Trigger dormancy hitlist assigned notification
   */
  async triggerDormancyHitlistAssigned(
    hitlistRecordId: string,
    assignedUserId: string,
    triggerUserId?: string,
  ): Promise<void> {
    await this.triggerNotification(NotificationType.DORMANCY_HITLIST_ASSIGNED, {
      entityType: 'dormancy_hitlist',
      entityId: hitlistRecordId,
      entityData: { assignedUserId },
      triggerUserId,
    });
  }

  /**
   * Trigger two by two hitlist assigned notification
   */
  async triggerTwoByTwoHitlistAssigned(
    phaseId: string,
    assignedUserId: string,
    triggerUserId?: string,
  ): Promise<void> {
    await this.triggerNotification(NotificationType.TWO_BY_TWO_HITLIST_ASSIGNED, {
      entityType: 'two_by_two_hitlist',
      entityId: phaseId,
      entityData: { assignedUserId },
      triggerUserId,
    });
  }

  /**
   * Trigger custom notification
   */
  async triggerCustomNotification(
    recipientIds: string[],
    title: string,
    message: string,
    data?: Record<string, any>,
    senderId?: string,
    entityType?: string,
    entityId?: string,
  ): Promise<void> {
    try {
      const notifications = recipientIds.map(recipientId => ({
        type: NotificationType.CUSTOM,
        title,
        message,
        data,
        recipient_id: recipientId,
        sender_id: senderId,
        entity_type: entityType,
        entity_id: entityId,
      }));

      await this.notificationsService.createBatch(notifications);
      
      this.logger.log(`Successfully triggered ${notifications.length} custom notifications`);

    } catch (error) {
      this.logger.error(`Failed to trigger custom notification: ${error.message}`, error.stack);
    }
  }
}
