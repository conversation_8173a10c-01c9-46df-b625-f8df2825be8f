import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import {
  NotificationHandler,
  NotificationContext,
} from './notification.handler.interface';
import { NotificationType, NotificationPriority } from '@prisma/client';

@Injectable()
export class ActivityCreatedHandler implements NotificationHandler {
  private readonly logger = new Logger(ActivityCreatedHandler.name);
  readonly type = NotificationType.ACTIVITY_CREATED;

  constructor(private readonly prisma: PrismaService) {}

  shouldHandle(context: NotificationContext): boolean {
    return (
      context.entityType === 'activity' && context.entityData?.isNewActivity
    );
  }

  async generateNotification(context: NotificationContext): Promise<{
    title: string;
    message: string;
    priority: NotificationPriority;
    data?: Record<string, any>;
    recipientIds: string[];
  }> {
    const { entityId, triggerUserId } = context;

    try {
      // Get the activity details
      const activity = await this.prisma.activity.findUnique({
        where: { id: entityId },
        include: {
          lead: {
            select: {
              id: true,
              customer_name: true,
              rm_user_id: true,
              assigned_user: true,
            },
          },
          performed_by: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
          purpose: {
            select: {
              name: true,
            },
          },
          branch: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!activity) {
        this.logger.warn(`Activity ${entityId} not found for notification`);
        return {
          title: '',
          message: '',
          priority: NotificationPriority.NORMAL,
          recipientIds: [],
        };
      }

      const recipientIds: string[] = [];

      // Notify the RM user if the activity is related to a lead
      if (
        activity.lead?.rm_user_id &&
        activity.lead.rm_user_id !== activity.performed_by_user_id
      ) {
        recipientIds.push(activity.lead.rm_user_id);
      }

      // Notify the assigned user if different from RM user and performer
      if (
        activity.lead?.assigned_user &&
        activity.lead.assigned_user !== activity.performed_by_user_id &&
        !recipientIds.includes(activity.lead.assigned_user)
      ) {
        recipientIds.push(activity.lead.assigned_user);
      }

      // If no specific recipients, notify branch managers or supervisors
      if (recipientIds.length === 0 && activity.branch_id) {
        const branchManagers = await this.prisma.user.findMany({
          where: {
            branch_id: activity.branch_id,
            role: {
              name: {
                contains: 'manager',
                mode: 'insensitive',
              },
            },
          },
          select: {
            id: true,
          },
        });

        recipientIds.push(...branchManagers.map((manager) => manager.id));
      }

      const title = 'New Activity Created';
      const message = `A new ${activity.activity_type} activity has been created for ${activity.lead?.customer_name || 'a customer'}`;

      const data = {
        activityId: activity.id,
        activityType: activity.activity_type,
        interactionType: activity.interaction_type,
        callStatus: activity.call_status,
        visitStatus: activity.visit_status,
        notes: activity.notes,
        callDurationMinutes: activity.call_duration_minutes,
        nextFollowupDate: activity.next_followup_date,
        followupStatus: activity.followup_status,
        performedBy: activity.performed_by.name,
        performedByRmCode: activity.performed_by.rm_code,
        purpose: activity.purpose?.name,
        branchName: activity.branch?.name,
        leadId: activity.lead?.id,
        customerName: activity.lead?.customer_name,
        createdAt: activity.created_at,
      };

      return {
        title,
        message,
        priority: NotificationPriority.LOW,
        data,
        recipientIds,
      };
    } catch (error) {
      this.logger.error(
        `Failed to generate activity created notification: ${error.message}`,
        error.stack,
      );
      return {
        title: '',
        message: '',
        priority: NotificationPriority.NORMAL,
        recipientIds: [],
      };
    }
  }
}
