import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { QueueService } from '../queue/queue.service';
import {
  NotificationType,
  NotificationPriority,
  Prisma,
} from '@prisma/client';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { NotificationResponseDto } from './dto/notification-response.dto';
import { NotificationFiltersDto } from './dto/notification-filters.dto';
import { NotificationCountDto } from './dto/notification-count.dto';
import { PaginatedResponseDto } from '../common/dto/pagination.dto';

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly queueService: QueueService,
  ) {}

  /**
   * Create a new notification
   */
  async create(createNotificationDto: CreateNotificationDto): Promise<NotificationResponseDto | null> {
    try {
      this.logger.log(`Creating notification of type ${createNotificationDto.type} for user ${createNotificationDto.recipient_id}`);

      // Validate recipient exists
      const recipient = await this.prisma.user.findUnique({
        where: { id: createNotificationDto.recipient_id },
      });

      if (!recipient) {
        throw new NotFoundException(`User with ID ${createNotificationDto.recipient_id} not found`);
      }

      // Validate sender if provided
      if (createNotificationDto.sender_id) {
        const sender = await this.prisma.user.findUnique({
          where: { id: createNotificationDto.sender_id },
        });

        if (!sender) {
          throw new NotFoundException(`Sender with ID ${createNotificationDto.sender_id} not found`);
        }
      }

      // Check user notification preferences
      const preference = await this.prisma.notificationPreference.findUnique({
        where: {
          user_id_notification_type: {
            user_id: createNotificationDto.recipient_id,
            notification_type: createNotificationDto.type,
          },
        },
      });

      // If preference exists and is disabled, don't create the notification
      if (preference && !preference.enabled) {
        this.logger.log(`Notification type ${createNotificationDto.type} is disabled for user ${createNotificationDto.recipient_id}`);
        return null;
      }

      const notification = await this.prisma.notification.create({
        data: {
          type: createNotificationDto.type,
          title: createNotificationDto.title,
          message: createNotificationDto.message,
          data: createNotificationDto.data || {},
          recipient_id: createNotificationDto.recipient_id,
          sender_id: createNotificationDto.sender_id,
          entity_type: createNotificationDto.entity_type,
          entity_id: createNotificationDto.entity_id,
          priority: createNotificationDto.priority || NotificationPriority.NORMAL,
          expires_at: createNotificationDto.expires_at ? new Date(createNotificationDto.expires_at) : null,
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
          recipient: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
        },
      });

      // Queue email notification if enabled (fire-and-forget)
      if (!preference || preference.email_enabled) {
        this.queueService.addNotificationJob({
          notificationId: notification.id,
          type: 'email',
          recipientEmail: recipient.email,
          recipientName: recipient.name,
          notificationType: createNotificationDto.type,
          title: createNotificationDto.title,
          message: createNotificationDto.message,
          data: createNotificationDto.data,
        }).catch(error => {
          this.logger.error(`Failed to queue email notification: ${error.message}`);
        });
      }

      this.logger.log(`Notification created successfully: ${notification.id}`);
      return this.transformToResponseDto(notification);

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      this.logger.error(`Failed to create notification: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to create notification');
    }
  }

  /**
   * Create multiple notifications in batch
   */
  async createBatch(notifications: CreateNotificationDto[]): Promise<NotificationResponseDto[]> {
    this.logger.log(`Creating ${notifications.length} notifications in batch`);

    const results: NotificationResponseDto[] = [];
    
    for (const notificationDto of notifications) {
      try {
        const notification = await this.create(notificationDto);
        if (notification) {
          results.push(notification);
        }
      } catch (error) {
        this.logger.error(`Failed to create notification in batch: ${error.message}`);
        // Continue with other notifications even if one fails
      }
    }

    this.logger.log(`Successfully created ${results.length} out of ${notifications.length} notifications`);
    return results;
  }

  /**
   * Get notifications for a user with filtering and pagination
   */
  async findByUser(
    userId: string,
    filters: NotificationFiltersDto = {},
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponseDto<NotificationResponseDto>> {
    try {
      const where: Prisma.NotificationWhereInput = {
        recipient_id: userId,
        ...this.buildWhereClause(filters),
      };

      const [notifications, total] = await Promise.all([
        this.prisma.notification.findMany({
          where,
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                email: true,
                rm_code: true,
              },
            },
            recipient: {
              select: {
                id: true,
                name: true,
                email: true,
                rm_code: true,
              },
            },
          },
          orderBy: { created_at: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
        }),
        this.prisma.notification.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        data: notifications.map(notification => this.transformToResponseDto(notification)),
        meta: {
          page,
          limit,
          total,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1,
        },
      };

    } catch (error) {
      this.logger.error(`Failed to fetch notifications for user ${userId}: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to fetch notifications');
    }
  }

  /**
   * Get notification count for a user
   */
  async getCount(userId: string): Promise<NotificationCountDto> {
    try {
      const [unreadCount, totalCount, byType, byPriority] = await Promise.all([
        this.prisma.notification.count({
          where: {
            recipient_id: userId,
            is_read: false,
            OR: [
              { expires_at: null },
              { expires_at: { gt: new Date() } },
            ],
          },
        }),
        this.prisma.notification.count({
          where: {
            recipient_id: userId,
            OR: [
              { expires_at: null },
              { expires_at: { gt: new Date() } },
            ],
          },
        }),
        this.prisma.notification.groupBy({
          by: ['type'],
          where: {
            recipient_id: userId,
            is_read: false,
            OR: [
              { expires_at: null },
              { expires_at: { gt: new Date() } },
            ],
          },
          _count: { type: true },
        }),
        this.prisma.notification.groupBy({
          by: ['priority'],
          where: {
            recipient_id: userId,
            is_read: false,
            OR: [
              { expires_at: null },
              { expires_at: { gt: new Date() } },
            ],
          },
          _count: { priority: true },
        }),
      ]);

      const byTypeMap = byType.reduce((acc, item) => {
        acc[item.type] = item._count.type;
        return acc;
      }, {} as Record<string, number>);

      const byPriorityMap = byPriority.reduce((acc, item) => {
        acc[item.priority] = item._count.priority;
        return acc;
      }, {} as Record<string, number>);

      return {
        unread_count: unreadCount,
        total_count: totalCount,
        by_type: byTypeMap,
        by_priority: byPriorityMap,
      };

    } catch (error) {
      this.logger.error(`Failed to get notification count for user ${userId}: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get notification count');
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string, userId: string): Promise<NotificationResponseDto> {
    try {
      const notification = await this.prisma.notification.findFirst({
        where: {
          id: notificationId,
          recipient_id: userId,
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
          recipient: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
        },
      });

      if (!notification) {
        throw new NotFoundException('Notification not found or access denied');
      }

      if (notification.is_read) {
        return this.transformToResponseDto(notification);
      }

      const updatedNotification = await this.prisma.notification.update({
        where: { id: notificationId },
        data: {
          is_read: true,
          read_at: new Date(),
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
          recipient: {
            select: {
              id: true,
              name: true,
              email: true,
              rm_code: true,
            },
          },
        },
      });

      this.logger.log(`Notification ${notificationId} marked as read by user ${userId}`);
      return this.transformToResponseDto(updatedNotification);

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      this.logger.error(`Failed to mark notification as read: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to mark notification as read');
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: string): Promise<{ count: number }> {
    try {
      const result = await this.prisma.notification.updateMany({
        where: {
          recipient_id: userId,
          is_read: false,
        },
        data: {
          is_read: true,
          read_at: new Date(),
        },
      });

      this.logger.log(`Marked ${result.count} notifications as read for user ${userId}`);
      return { count: result.count };

    } catch (error) {
      this.logger.error(`Failed to mark all notifications as read: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to mark all notifications as read');
    }
  }

  /**
   * Delete a notification
   */
  async delete(notificationId: string, userId: string): Promise<void> {
    try {
      const notification = await this.prisma.notification.findFirst({
        where: {
          id: notificationId,
          recipient_id: userId,
        },
      });

      if (!notification) {
        throw new NotFoundException('Notification not found or access denied');
      }

      await this.prisma.notification.delete({
        where: { id: notificationId },
      });

      this.logger.log(`Notification ${notificationId} deleted by user ${userId}`);

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      this.logger.error(`Failed to delete notification: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to delete notification');
    }
  }

  /**
   * Clean up expired notifications
   */
  async cleanupExpired(): Promise<{ count: number }> {
    try {
      const result = await this.prisma.notification.deleteMany({
        where: {
          expires_at: {
            lt: new Date(),
          },
        },
      });

      this.logger.log(`Cleaned up ${result.count} expired notifications`);
      return { count: result.count };

    } catch (error) {
      this.logger.error(`Failed to cleanup expired notifications: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to cleanup expired notifications');
    }
  }

  /**
   * Build where clause for filtering
   */
  private buildWhereClause(filters: NotificationFiltersDto): Prisma.NotificationWhereInput {
    const where: Prisma.NotificationWhereInput = {};

    if (filters.type) {
      where.type = filters.type;
    }

    if (filters.is_read !== undefined) {
      where.is_read = filters.is_read;
    }

    if (filters.priority) {
      where.priority = filters.priority;
    }

    if (filters.entity_type) {
      where.entity_type = filters.entity_type;
    }

    if (filters.entity_id) {
      where.entity_id = filters.entity_id;
    }

    if (filters.sender_id) {
      where.sender_id = filters.sender_id;
    }

    if (filters.created_after || filters.created_before) {
      where.created_at = {};
      if (filters.created_after) {
        where.created_at.gte = new Date(filters.created_after);
      }
      if (filters.created_before) {
        where.created_at.lte = new Date(filters.created_before);
      }
    }

    if (filters.search) {
      where.OR = [
        { title: { contains: filters.search, mode: 'insensitive' } },
        { message: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    // Always exclude expired notifications unless explicitly requested
    where.OR = [
      { expires_at: null },
      { expires_at: { gt: new Date() } },
    ];

    return where;
  }

  /**
   * Transform Prisma notification to response DTO
   */
  private transformToResponseDto(notification: any): NotificationResponseDto {
    return {
      id: notification.id,
      type: notification.type,
      title: notification.title,
      message: notification.message,
      recipient_id: notification.recipient_id,
      sender_id: notification.sender_id,
      entity_type: notification.entity_type,
      entity_id: notification.entity_id,
      is_read: notification.is_read,
      read_at: notification.read_at,
      priority: notification.priority,
      expires_at: notification.expires_at,
      created_at: notification.created_at,
      updated_at: notification.updated_at,
      sender: notification.sender,
      recipient: notification.recipient,
    };
  }
}
