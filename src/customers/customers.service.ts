import { Injectable, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CustomerResponseDto } from './dto/customer-response.dto';
import * as ExcelJS from 'exceljs';

/**
 * Service handling customer-related business logic
 * Provides customer data from leads table with RBAC implementation
 */
@Injectable()
export class CustomersService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Gets customers from leads table based on user permissions
   * Implements RBAC: view.all.customers, view.branch.customers, view.my.customers
   * Only includes leads that have an account number (converted leads)
   */
  async getCustomers(user?: {
    id: string;
    branch_id: string;
    permissions: string[];
  }): Promise<CustomerResponseDto[]> {
    // Build base where clause - only customers (leads with account numbers)
    const whereClause: any = {
      account_number: {
        not: null,
      },
    };

    // Apply RBAC filtering based on user permissions
    if (user && user.permissions) {
      const canViewAllCustomers = user.permissions.includes('view.all.customers');
      const canViewRegionCustomers = user.permissions.includes('view.region.customers');
      const canViewBranchCustomers = user.permissions.includes('view.branch.customers');
      const canViewMyCustomers = user.permissions.includes('view.my.customers');

      if (!canViewAllCustomers && !canViewRegionCustomers && !canViewBranchCustomers && !canViewMyCustomers) {
        // User has no permission to view customers
        throw new BadRequestException('Insufficient permissions to view customers');
      }

      if (canViewAllCustomers) {
        // User can see all customers - no additional filtering needed
      } else if (canViewRegionCustomers) {
        // User can see customers in their region
        const userBranch = await this.prisma.branch.findUnique({
          where: { id: user.branch_id },
          select: { region_id: true },
        });
        if (!userBranch?.region_id) {
          throw new BadRequestException('User branch has no region configured');
        }
        whereClause.branch = { region_id: userBranch.region_id };
      } else if (canViewBranchCustomers) {
        // User can see customers in their branch PLUS their own customers from any branch
        whereClause.OR = [
          { branch_id: user.branch_id }, // All customers in user's branch
          { 
            OR: [
              { account_number_assigned_by: user.id }, // Customers converted by this user
              { rm_user_id: user.id }, // Customers assigned to this user as RM
            ]
          }
        ];
      } else if (canViewMyCustomers) {
        // User can only see their own customers (converted by them or assigned as RM)
        whereClause.OR = [
          { account_number_assigned_by: user.id }, // Customers converted by this user
          { rm_user_id: user.id }, // Customers assigned to this user as RM
        ];
      }
    }

    const leads = await this.prisma.lead.findMany({
      where: whereClause,
      select: {
        id: true,
        account_number: true,
        account_number_assigned_at: true,
        customer_name: true,
        phone_number: true,
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
        rm_user: {
          select: {
            id: true,
            name: true,
            rm_code: true,
          },
        },
        account_number_assigned_by_user: {
          select: {
            id: true,
            name: true,
            rm_code: true,
          },
        },
      },
      orderBy: {
        customer_name: 'asc',
      },
    });

    return leads.map((lead) => ({
      id: lead.id,
      account_number: lead.account_number,
      account_number_assigned_at: lead.account_number_assigned_at,
      name: lead.customer_name || 'Unknown Customer',
      phone_number: lead.phone_number,
      branch: lead.branch
        ? {
            id: lead.branch.id,
            name: lead.branch.name,
          }
        : null,
      rm_user: lead.rm_user
        ? {
            id: lead.rm_user.id,
            name: lead.rm_user.name,
            rm_code: lead.rm_user.rm_code,
          }
        : null,
      account_number_assigned_by: lead.account_number_assigned_by_user
        ? {
            id: lead.account_number_assigned_by_user.id,
            name: lead.account_number_assigned_by_user.name,
            rm_code: lead.account_number_assigned_by_user.rm_code,
          }
        : null,
    }));
  }

  /**
   * Exports customers to Excel file based on user permissions
   * Implements the same RBAC logic as getCustomers
   * @param user - Optional user object with permissions for RBAC filtering
   * @returns Buffer containing the Excel file
   */
  async exportCustomers(user?: {
    id: string;
    branch_id: string;
    permissions: string[];
  }): Promise<Buffer> {
    // Build base where clause - only customers (leads with account numbers)
    const whereClause: any = {
      account_number: {
        not: null,
      },
    };

    // Apply RBAC filtering based on user permissions
    if (user && user.permissions) {
      const canViewAllCustomers = user.permissions.includes('view.all.customers');
      const canViewRegionCustomers = user.permissions.includes('view.region.customers');
      const canViewBranchCustomers = user.permissions.includes('view.branch.customers');
      const canViewMyCustomers = user.permissions.includes('view.my.customers');

      if (!canViewAllCustomers && !canViewRegionCustomers && !canViewBranchCustomers && !canViewMyCustomers) {
        // User has no permission to export customers
        throw new BadRequestException('Insufficient permissions to export customers');
      }

      if (canViewAllCustomers) {
        // User can see all customers - no additional filtering needed
      } else if (canViewRegionCustomers) {
        // User can see customers in their region
        const userBranch = await this.prisma.branch.findUnique({
          where: { id: user.branch_id },
          select: { region_id: true },
        });
        if (!userBranch?.region_id) {
          throw new BadRequestException('User branch has no region configured');
        }
        whereClause.branch = { region_id: userBranch.region_id };
      } else if (canViewBranchCustomers) {
        // User can see customers in their branch PLUS their own customers from any branch
        whereClause.OR = [
          { branch_id: user.branch_id }, // All customers in user's branch
          { 
            OR: [
              { account_number_assigned_by: user.id }, // Customers converted by this user
              { rm_user_id: user.id }, // Customers assigned to this user as RM
            ]
          }
        ];
      } else if (canViewMyCustomers) {
        // User can only see their own customers (converted by them or assigned as RM)
        whereClause.OR = [
          { account_number_assigned_by: user.id }, // Customers converted by this user
          { rm_user_id: user.id }, // Customers assigned to this user as RM
        ];
      }
    }

    // Get customers data with RBAC filtering applied
    const customers = await this.prisma.lead.findMany({
      where: whereClause,
      select: {
        id: true,
        account_number: true,
        account_number_assigned_at: true,
        customer_name: true,
        phone_number: true,
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
        rm_user: {
          select: {
            id: true,
            name: true,
            rm_code: true,
          },
        },
        account_number_assigned_by_user: {
          select: {
            id: true,
            name: true,
            rm_code: true,
          },
        },
      },
      orderBy: {
        customer_name: 'asc',
      },
    });

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Customers Export');

    // Define columns with proper headers
    worksheet.columns = [
      { header: 'Customer Name', key: 'name', width: 30 },
      { header: 'Account Number', key: 'accountNumber', width: 20 },
      { header: 'Phone Number', key: 'phoneNumber', width: 20 },
      { header: 'Branch', key: 'branch', width: 25 },
      { header: 'RM User', key: 'rmUser', width: 25 },
      { header: 'Converted Date', key: 'accountAssignedDate', width: 25 },
      { header: 'Converted By', key: 'convertedBy', width: 25 },
    ];

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '366092' },
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };

    // Transform customers data to match the expected format
    const transformedCustomers = customers.map((lead) => ({
      id: lead.id,
      account_number: lead.account_number,
      account_number_assigned_at: lead.account_number_assigned_at,
      name: lead.customer_name || 'Unknown Customer',
      phone_number: lead.phone_number,
      branch: lead.branch
        ? {
            id: lead.branch.id,
            name: lead.branch.name,
          }
        : null,
      rm_user: lead.rm_user
        ? {
            id: lead.rm_user.id,
            name: lead.rm_user.name,
            rm_code: lead.rm_user.rm_code,
          }
        : null,
      account_number_assigned_by: lead.account_number_assigned_by_user
        ? {
            id: lead.account_number_assigned_by_user.id,
            name: lead.account_number_assigned_by_user.name,
            rm_code: lead.account_number_assigned_by_user.rm_code,
          }
        : null,
    }));

    // Add data rows
    transformedCustomers.forEach((customer: any) => {
      worksheet.addRow({
        accountNumber: customer.account_number || '',
        name: customer.name || 'Unknown Customer',
        phoneNumber: customer.phone_number || '',
        branch: customer.branch?.name || '',
        rmUser: customer.rm_user?.name || '',
        accountAssignedDate: customer.account_number_assigned_at
          ? this.formatDate(new Date(customer.account_number_assigned_at))
          : '',
        convertedBy: customer.account_number_assigned_by?.name || '',
      });
    });

    // Auto-fit columns and add borders
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        // Alternate row colors for better readability
        if (rowNumber > 1 && rowNumber % 2 === 0) {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F8F9FA' },
          };
        }
      });
    });

    // Add summary information at the bottom
    const summaryStartRow = customers.length + 3;
    worksheet.getCell(`A${summaryStartRow}`).value = 'Export Summary:';
    worksheet.getCell(`A${summaryStartRow}`).font = { bold: true };
    worksheet.getCell(`A${summaryStartRow + 1}`).value = `Total Customers: ${customers.length}`;
    worksheet.getCell(`A${summaryStartRow + 2}`).value = `Export Date: ${this.formatDate(new Date())}`;

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return Buffer.from(buffer);
  }

  /**
   * Helper method to format dates in 'Sep 1, 2025' format
   * @param date - Date to format
   * @returns Formatted date string
   */
  private formatDate(date: Date): string {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    const month = months[date.getMonth()];
    const day = date.getDate();
    const year = date.getFullYear();
    
    return `${month} ${day}, ${year}`;
  }
}
