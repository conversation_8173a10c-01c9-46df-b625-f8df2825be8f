import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { FileStorageService } from '../common/services/file-storage.service';
import {
  FollowUpResponseDto,
  MakeVisitOrCallDto,
  RescheduleFollowUpDto,
  RescheduleResponseDto,
  LeadDetailsResponseDto,
} from './dto/follow-up-response.dto';
import { InteractionHistoryResponseDto } from '../activities/dto/interaction-history-response.dto';
import { TargetUpdateService } from 'src/targets/services/target-update.service';

/**
 * Service handling all follow-up related business logic
 */
@Injectable()
export class FollowUpsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly fileStorageService: FileStorageService,
    private readonly targetUpdateService: TargetUpdateService,
  ) {}

  /**
   * Gets follow-ups for today, upcoming, completed, overdue, or canceled based on type and user permissions
   */
  async getFollowUps(
    type: 'today' | 'upcoming' | 'completed' | 'overdue' | 'canceled',
    userId: string,
    userPermissions: string[],
    userBranchId: string,
  ): Promise<FollowUpResponseDto[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Determine date filter based on type
    let dateFilter: any;
    let baseFilter: any = { canceled_at: null }; // Exclude canceled by default

    if (type === 'today') {
      dateFilter = {
        for_date: {
          gte: today,
          lt: tomorrow,
        },
      };
    } else if (type === 'upcoming') {
      dateFilter = {
        for_date: {
          gt: tomorrow,
        },
      };
    } else if (type === 'completed') {
      dateFilter = {
        date_completed: {
          not: null,
        },
      };
    } else if (type === 'overdue') {
      dateFilter = {
        for_date: {
          lt: today,
        },
        date_completed: null,
      };
    } else if (type === 'canceled') {
      baseFilter = { canceled_at: { not: null } };
      dateFilter = {};
    }

    // Determine permission-based filter
    let userFilter: any = {};
    if (userPermissions.includes('followups.all.branches')) {
      // No additional filter - can see all follow-ups
    } else if (userPermissions.includes('followups.my.region')) {
      // Can see follow-ups from all branches in their region
      const userBranch = await this.prisma.branch.findUnique({
        where: { id: userBranchId },
        select: { region_id: true },
      });

      if (userBranch) {
        const regionBranches = await this.prisma.branch.findMany({
          where: { region_id: userBranch.region_id },
          select: { id: true },
        });

        userFilter = {
          branch_id: {
            in: regionBranches.map((branch) => branch.id),
          },
        };
      } else {
        // Fallback to user's branch if region not found
        userFilter = { branch_id: userBranchId };
      }
    } else if (userPermissions.includes('followups.my.branch')) {
      userFilter = { branch_id: userBranchId };
    } else {
      userFilter = { user_id: userId };
    }

    // Get follow-ups
    const followUps = await this.prisma.followUp.findMany({
      where: {
        ...baseFilter,
        ...dateFilter,
        ...userFilter,
      },
      include: {
        lead: {
          select: {
            customer_name: true,
          },
        },
        user: {
          select: {
            name: true,
          },
        },
      },
      orderBy:
        type === 'today'
          ? [{ date_completed: 'asc' }, { for_date: 'asc' }]
          : type === 'completed'
            ? { date_completed: 'desc' }
            : type === 'overdue'
              ? { for_date: 'asc' }
              : type === 'canceled'
                ? { canceled_at: 'desc' }
                : { for_date: 'asc' },
    });

    // Add overdue follow-ups for today type
    let overdueFollowUps: any[] = [];
    if (type === 'today') {
      overdueFollowUps = await this.prisma.followUp.findMany({
        where: {
          for_date: {
            lt: today,
          },
          date_completed: null,
          canceled_at: null,
          ...userFilter,
        },
        include: {
          lead: {
            select: {
              customer_name: true,
            },
          },
          user: {
            select: {
              name: true,
            },
          },
        },
        orderBy: { for_date: 'asc' },
      });
    }

    // Combine and format results
    const allFollowUps = [...overdueFollowUps, ...followUps];

    // Check if user has permissions to see agent names
    const hasPermissions =
      userPermissions.includes('followups.all.branches') ||
      userPermissions.includes('followups.my.region') ||
      userPermissions.includes('followups.my.branch');

    return allFollowUps.map((followUp) => {
      const result: any = {
        id: followUp.id,
        customer_name: followUp.lead?.customer_name || 'Unknown',
        followup_date: followUp.for_date.toISOString(),
        date_completed: followUp.date_completed?.toISOString() || null,
        type: followUp.type || 'unknown', // Use the type field from FollowUp
        status: overdueFollowUps.includes(followUp)
          ? 'Overdue'
          : type === 'canceled'
            ? 'Canceled'
            : 'Medium',
        lead_id: followUp.lead_id,
      };

      // Add agent_name only if user has permissions
      if (hasPermissions) {
        result.agent_name = followUp.user?.name || 'Unknown';
      }

      // Add canceled_at for canceled type
      if (type === 'canceled' && followUp.canceled_at) {
        result.canceled_at = followUp.canceled_at.toISOString();
      }

      return result;
    });
  }

  /**
   * Creates an activity from a follow-up
   */
  async makeVisitOrCall(
    followUpId: string,
    dto: MakeVisitOrCallDto,
    attachments: Express.Multer.File[] = [],
    userId: string,
  ): Promise<any> {
    // Get the follow-up with related data
    const followUp = await this.prisma.followUp.findUnique({
      where: { id: followUpId },
      include: {
        parent_activity: {
          select: {
            interaction_type: true,
          },
        },
      },
    });

    if (!followUp) {
      throw new NotFoundException(
        `Follow-up with ID '${followUpId}' not found`,
      );
    }

    // Validate purpose exists
    if (dto.purpose_id) {
      const purpose = await this.prisma.purposeOfActivity.findUnique({
        where: { id: dto.purpose_id },
      });
      if (!purpose) {
        throw new NotFoundException(
          `Purpose with ID '${dto.purpose_id}' not found`,
        );
      }
    }

    return await this.prisma.$transaction(async (tx) => {
      // Create the activity
      const activity = await tx.activity.create({
        data: {
          lead_id: followUp.lead_id,
          activity_type: 'Follow Up',
          interaction_type:
            followUp.parent_activity?.interaction_type || 'call',
          call_status: dto.call_status || null,
          visit_status: dto.visit_status || null,
          notes: dto.notes || null,
          performed_by_user_id: userId,
          purpose_id: dto.purpose_id,
          follow_up_id: followUpId,
          branch_id: followUp.branch_id,
        },
      });

      // Handle file attachments if provided
      if (attachments && attachments.length > 0) {
        const savedFiles = await this.fileStorageService.saveFiles(
          attachments,
          `follow-ups/${followUpId}`,
        );

        await tx.activityAttachment.createMany({
          data: savedFiles.map((savedFile) => ({
            activity_id: activity.id,
            file_url: savedFile.url,
          })),
        });
      }

      // Update follow-up completion if status is Success
      if (
        dto.call_status === 'Success' ||
        dto.visit_status === 'Success' ||
        dto.call_status === 'Successful' ||
        dto.visit_status === 'Successful'
      ) {
        await tx.followUp.update({
          where: { id: followUpId },
          data: { date_completed: new Date() },
        });
      }

      // UPDATE THE USER TARGETS IF CALL WAS SUCCESSFUL
      let targetActivity: string = 'LEADS';
      if (dto.call_status && dto.call_status.toLowerCase() === 'success') {
        // CALL THE TARGET UPDATE FUNCTION
        await this.targetUpdateService.updateUserTargets(
          targetActivity,
          'call',
          userId,
        );
      }

      if (dto.visit_status) {
        // CALL THE TARGET UPDATE FUNCTION
        await this.targetUpdateService.updateUserTargets(
          targetActivity,
          'visit',
          userId,
        );
      }

      return activity;
    });
  }

  /**
   * Reschedules a follow-up
   */
  async rescheduleFollowUp(
    followUpId: string,
    dto: RescheduleFollowUpDto,
  ): Promise<RescheduleResponseDto> {
    const followUp = await this.prisma.followUp.findUnique({
      where: { id: followUpId },
    });

    if (!followUp) {
      throw new NotFoundException(
        `Follow-up with ID '${followUpId}' not found`,
      );
    }

    const newDate = new Date(dto.new_date);
    if (isNaN(newDate.getTime())) {
      throw new BadRequestException('Invalid date format');
    }

    const updatedFollowUp = await this.prisma.followUp.update({
      where: { id: followUpId },
      data: { for_date: newDate },
    });

    return {
      for_date: updatedFollowUp.for_date.toISOString(),
    };
  }

  /**
   * Gets interaction history for a follow-up (same as lead interaction history)
   */
  async getInteractionHistory(
    followUpId: string,
  ): Promise<InteractionHistoryResponseDto[]> {
    const followUp = await this.prisma.followUp.findUnique({
      where: { id: followUpId },
      select: { lead_id: true },
    });

    if (!followUp) {
      throw new NotFoundException(
        `Follow-up with ID '${followUpId}' not found`,
      );
    }

    // Fetch all activities linked to this follow-up
    const activities = await this.prisma.activity.findMany({
      where: { follow_up_id: followUpId },
      include: {
        performed_by: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    return activities.map((activity) => ({
      interaction_type: activity.interaction_type || 'unknown',
      activity_type: activity.activity_type,
      next_followup_date: activity.next_followup_date?.toISOString() || null,
      notes: activity.notes,
      performed_by: activity.performed_by.name,
      created_at: activity.created_at.toISOString(),
    }));
  }

  /**
   * Gets follow-up counts by type based on user permissions
   */
  async getFollowUpCounts(
    userId: string,
    userPermissions: string[],
    userBranchId: string,
  ): Promise<{
    today_count: number;
    upcoming_count: number;
    completed_count: number;
    overdue_count: number;
    canceled_count: number;
  }> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Determine permission-based filter
    let userFilter: any = {};
    if (userPermissions.includes('followups.all.branches')) {
      // No additional filter - can see all follow-ups
    } else if (userPermissions.includes('followups.my.region')) {
      // Can see follow-ups from all branches in their region
      const userBranch = await this.prisma.branch.findUnique({
        where: { id: userBranchId },
        select: { region_id: true },
      });

      if (userBranch) {
        const regionBranches = await this.prisma.branch.findMany({
          where: { region_id: userBranch.region_id },
          select: { id: true },
        });

        userFilter = {
          branch_id: {
            in: regionBranches.map((branch) => branch.id),
          },
        };
      } else {
        // Fallback to user's branch if region not found
        userFilter = { branch_id: userBranchId };
      }
    } else if (userPermissions.includes('followups.my.branch')) {
      userFilter = { branch_id: userBranchId };
    } else {
      userFilter = { user_id: userId };
    }

    // Get counts for each type
    const [
      todayCount,
      upcomingCount,
      completedCount,
      overdueCount,
      canceledCount,
    ] = await Promise.all([
      // Today's follow-ups
      this.prisma.followUp.count({
        where: {
          for_date: {
            gte: today,
            lt: tomorrow,
          },
          canceled_at: null,
          ...userFilter,
        },
      }),
      // Upcoming follow-ups
      this.prisma.followUp.count({
        where: {
          for_date: {
            gt: tomorrow,
          },
          canceled_at: null,
          ...userFilter,
        },
      }),
      // Completed follow-ups
      this.prisma.followUp.count({
        where: {
          date_completed: {
            not: null,
          },
          canceled_at: null,
          ...userFilter,
        },
      }),
      // Overdue follow-ups (add to today count)
      this.prisma.followUp.count({
        where: {
          for_date: {
            lt: today,
          },
          date_completed: null,
          canceled_at: null,
          ...userFilter,
        },
      }),
      // Canceled follow-ups
      this.prisma.followUp.count({
        where: {
          canceled_at: {
            not: null,
          },
          ...userFilter,
        },
      }),
    ]);

    return {
      today_count: todayCount + overdueCount,
      upcoming_count: upcomingCount,
      completed_count: completedCount,
      overdue_count: overdueCount,
      canceled_count: canceledCount,
    };
  }

  /**
   * Cancels a follow-up
   */
  async cancelFollowUp(followUpId: string): Promise<{ canceled_at: string }> {
    const followUp = await this.prisma.followUp.findUnique({
      where: { id: followUpId },
    });

    if (!followUp) {
      throw new NotFoundException(
        `Follow-up with ID '${followUpId}' not found`,
      );
    }

    const updatedFollowUp = await this.prisma.followUp.update({
      where: { id: followUpId },
      data: { canceled_at: new Date() },
    });

    return {
      canceled_at: updatedFollowUp.canceled_at!.toISOString(),
    };
  }

  /**
   * Gets lead details for a follow-up
   */
  async getLeadDetails(followUpId: string): Promise<LeadDetailsResponseDto> {
    const followUp = await this.prisma.followUp.findUnique({
      where: { id: followUpId },
      select: { lead_id: true },
    });

    if (!followUp) {
      throw new NotFoundException(
        `Follow-up with ID '${followUpId}' not found`,
      );
    }

    const lead = await this.prisma.lead.findUnique({
      where: { id: followUp.lead_id },
      include: {
        customer_category: {
          select: {
            id: true,
            name: true,
          },
        },
        isic_sector: {
          select: {
            id: true,
            name: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
            region: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        rm_user: {
          select: {
            id: true,
            name: true,
            email: true,
            rm_code: true,
          },
        },
        employer: {
          select: {
            id: true,
            name: true,
          },
        },
        anchor: {
          select: {
            id: true,
            name: true,
            email: true,
            phone_number: true,
          },
        },
        contact_persons: true,
      },
    });

    if (!lead) {
      throw new NotFoundException(`Lead not found for follow-up`);
    }

    return {
      id: lead.id,
      customer_name: lead.customer_name || '',
      lead_status: lead.lead_status || '',
      phone_number: lead.phone_number || '',
      type_of_lead: lead.type_of_lead || '',
      account_number: lead.account_number,
      account_number_assigned_at:
        lead.account_number_assigned_at?.toISOString() || null,
      customer_category: lead.customer_category || { id: '', name: '' },
      isic_sector: lead.isic_sector || { id: '', name: '' },
      branch: lead.branch || { id: '', name: '', region: { id: '', name: '' } },
      rm_user: lead.rm_user || { id: '', name: '', email: '', rm_code: '' },
      employer: lead.employer || { id: '', name: '' },
      anchor: lead.anchor || { id: '', name: '', email: '', phone_number: '' },
      contact_persons: lead.contact_persons || [],
      created_at: lead.created_at.toISOString(),
    };
  }
}
