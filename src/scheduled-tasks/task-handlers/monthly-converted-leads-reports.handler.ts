import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';
import { PrismaService } from '../../prisma/prisma.service';
import { EmailService } from '../../common/services/email.service';
import { isApplicable } from '../../common/utils/date.utils';
import { NotificationUtils } from '../../notifications/utils/notification.utils';
import { NotificationType } from '@prisma/client';
// Initialize pdfmake
const pdfMake = require('pdfmake/build/pdfmake');
const pdfFonts = require('pdfmake/build/vfs_fonts');

// Set fonts for pdfmake (pdfFonts contains the font data directly)
pdfMake.vfs = pdfFonts;

export interface MonthlyConvertedLeadsReportsPayload {
  // Empty payload - uses current date
}

interface ConvertedLead {
  id: string;
  customer_name: string | null;
  account_number: string | null;
  account_number_assigned_at: Date | null;
  branch: {
    id: string;
    name: string;
    region: {
      id: string;
      name: string;
    };
  } | null;
  account_number_assigned_by_user: {
    id: string;
    name: string;
  } | null;
}

interface NewLead {
  id: string;
  customer_name: string | null;
  created_at: Date;
  branch: {
    id: string;
    name: string;
    region: {
      id: string;
      name: string;
    };
  } | null;
  rm_user: {
    id: string;
    name: string;
  } | null;
}

interface UserWithPermissions {
  id: string;
  name: string;
  email: string;
  branch_id: string;
  branch: {
    id: string;
    name: string;
    region: {
      id: string;
      name: string;
    };
  };
  permissions: Array<{
    id: string;
  }>;
}

@Injectable()
export class MonthlyConvertedLeadsReportsHandler implements TaskHandler {
  private readonly logger = new Logger(
    MonthlyConvertedLeadsReportsHandler.name,
  );

  constructor(
    private readonly prisma: PrismaService,
    private readonly emailService: EmailService,
    private readonly notificationUtils: NotificationUtils,
  ) {}

  getTaskType(): string {
    return 'monthly-converted-leads-reports';
  }

  getDescription(): string {
    return 'Sends monthly converted leads reports to users with appropriate permissions';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    return { isValid: true, errors: [] };
  }

  async handle(
    payload: MonthlyConvertedLeadsReportsPayload,
    job: Job,
  ): Promise<any> {
    this.logger.log('Processing monthly converted leads reports');

    try {
      await job.updateProgress(5);

      // Check if today is applicable for sending reports (not weekend or holiday)
      const today = new Date();
      const applicable = await isApplicable(today, this.prisma);

      if (!applicable) {
        this.logger.log(
          'Skipping monthly converted leads reports - today is a weekend or holiday',
        );
        return {
          success: true,
          convertedCount: 0,
          newLeadsCount: 0,
          reportsSent: 0,
          usersProcessed: 0,
          skipped: true,
          reason: 'Weekend or holiday',
          processedAt: new Date().toISOString(),
        };
      }

      // Calculate month start (1st of current month) and end (current date)
      const now = new Date();
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      monthStart.setHours(0, 0, 0, 0);

      const monthEnd = new Date(now);
      monthEnd.setHours(23, 59, 59, 999);

      this.logger.log(
        `Processing monthly report from ${monthStart.toISOString()} to ${monthEnd.toISOString()}`,
      );

      await job.updateProgress(10);

      // Get converted leads for the month
      const convertedLeads = (await this.prisma.lead.findMany({
        where: {
          account_number: { not: null },
          account_number_assigned_at: {
            gte: monthStart,
            lte: monthEnd,
          },
        },
        include: {
          branch: {
            include: {
              region: true,
            },
          },
          account_number_assigned_by_user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: [
          { branch: { name: 'asc' } },
          { account_number_assigned_at: 'desc' },
        ],
      })) as any[];

      await job.updateProgress(20);

      // Get new leads for the month
      const newLeads = (await this.prisma.lead.findMany({
        where: {
          created_at: {
            gte: monthStart,
            lte: monthEnd,
          },
        },
        include: {
          branch: {
            include: {
              region: true,
            },
          },
          rm_user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: [{ branch: { name: 'asc' } }, { created_at: 'desc' }],
      })) as any[];

      await job.updateProgress(30);

      if (convertedLeads.length === 0 && newLeads.length === 0) {
        this.logger.log('No converted leads or new leads found for this month');
        return {
          success: true,
          convertedCount: 0,
          newLeadsCount: 0,
          reportsSent: 0,
          usersProcessed: 0,
          processedAt: new Date().toISOString(),
        };
      }

      this.logger.log(
        `Found ${convertedLeads.length} converted leads and ${newLeads.length} new leads for this month`,
      );

      // Get users with monthly converted leads report permissions
      const usersWithPermissions = (await this.prisma.user.findMany({
        where: {
          role: {
            role_permissions: {
              some: {
                permission: {
                  id: {
                    in: [
                      'reports.converted.leads.all.monthly',
                      'reports.converted.leads.region.monthly',
                      'reports.converted.leads.branch.monthly',
                    ],
                  },
                },
              },
            },
          },
        },
        include: {
          branch: {
            include: {
              region: true,
            },
          },
          role: {
            include: {
              role_permissions: {
                include: {
                  permission: {
                    select: {
                      id: true,
                    },
                  },
                },
              },
            },
          },
        },
      })) as any[];

      await job.updateProgress(35);

      if (usersWithPermissions.length === 0) {
        this.logger.log(
          'No users found with monthly converted leads report permissions',
        );
        return {
          success: true,
          convertedCount: convertedLeads.length,
          newLeadsCount: newLeads.length,
          reportsSent: 0,
          usersProcessed: 0,
          processedAt: new Date().toISOString(),
        };
      }

      this.logger.log(
        `Found ${usersWithPermissions.length} users with monthly converted leads report permissions`,
      );

      let reportsSent = 0;
      const progressStep = 60 / usersWithPermissions.length;

      // Process each user with permissions
      for (const user of usersWithPermissions) {
        try {
          const userPermissions = user.role.role_permissions.map(
            (rp: any) => rp.permission.id,
          );

          let reportScope = '';
          let filteredConvertedLeads: ConvertedLead[] = [];
          let filteredNewLeads: NewLead[] = [];

          // Apply permission hierarchy: all > region > branch
          if (userPermissions.includes('reports.converted.leads.all.monthly')) {
            reportScope = 'All Branches';
            filteredConvertedLeads = convertedLeads;
            filteredNewLeads = newLeads;
          } else if (
            userPermissions.includes('reports.converted.leads.region.monthly')
          ) {
            reportScope = `${user.branch.region.name}`;
            filteredConvertedLeads = convertedLeads.filter(
              (lead) => lead.branch.region.id === user.branch.region.id,
            );
            filteredNewLeads = newLeads.filter(
              (lead) => lead.branch.region.id === user.branch.region.id,
            );
          } else if (
            userPermissions.includes('reports.converted.leads.branch.monthly')
          ) {
            reportScope = `${user.branch.name}`;
            filteredConvertedLeads = convertedLeads.filter(
              (lead) => lead.branch.id === user.branch.id,
            );
            filteredNewLeads = newLeads.filter(
              (lead) => lead.branch.id === user.branch.id,
            );
          }

          console.log(`=== MONTHLY CONVERTED LEADS DEBUG ===`);
          console.log(`User: ${user.name} (${user.email})`);
          console.log(`Branch: ${user.branch.name} (${user.branch.id})`);
          console.log(
            `Region: ${user.branch.region.name} (${user.branch.region.id})`,
          );
          console.log(`Report Scope: ${reportScope}`);
          console.log(
            `User Permissions: ${userPermissions.filter((p) => p.includes('converted.leads')).join(', ')}`,
          );
          console.log(
            `Filtered Converted Leads: ${filteredConvertedLeads.length}`,
          );
          console.log(`Filtered New Leads: ${filteredNewLeads.length}`);

          if (filteredConvertedLeads.length > 0) {
            console.log(`Converted Leads Details:`);
            filteredConvertedLeads.forEach((lead) => {
              console.log(
                `  - ${lead.customer_name} (${lead.account_number}) - Branch: ${lead.branch?.name} (${lead.branch?.id})`,
              );
            });
          }

          if (filteredNewLeads.length > 0) {
            console.log(`New Leads Details:`);
            filteredNewLeads.forEach((lead) => {
              console.log(
                `  - ${lead.customer_name} - Branch: ${lead.branch?.name} (${lead.branch?.id})`,
              );
            });
          }

          if (
            filteredConvertedLeads.length > 0 ||
            filteredNewLeads.length > 0
          ) {
            console.log(`✅ Sending report to ${user.name} for ${reportScope}`);
            await this.generateAndSendReport(
              user,
              filteredConvertedLeads,
              filteredNewLeads,
              reportScope,
              monthStart,
              monthEnd,
              userPermissions.includes(
                'reports.converted.leads.branch.monthly',
              ),
            );
            reportsSent++;
            console.log(`✅ Report sent successfully to ${user.name}`);
          } else {
            console.log(`⚠️  No data to send report to ${user.name}`);
          }
        } catch (error) {
          console.error(
            `❌ ERROR generating report for user ${user.name} (${user.email}):`,
            error,
          );
          this.logger.error(
            `Failed to generate report for user ${user.id}:`,
            error,
          );
        }

        await job.updateProgress(35 + reportsSent * progressStep);
      }

      await job.updateProgress(100);

      this.logger.log(
        `Monthly converted leads reports task completed. Sent ${reportsSent} reports`,
      );

      return {
        success: true,
        convertedCount: convertedLeads.length,
        newLeadsCount: newLeads.length,
        reportsSent,
        usersProcessed: usersWithPermissions.length,
        processedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        'Failed to process monthly converted leads reports:',
        error,
      );
      throw error;
    }
  }

  private async generateAndSendReport(
    user: UserWithPermissions,
    convertedLeads: ConvertedLead[],
    newLeads: NewLead[],
    reportScope: string,
    monthStart: Date,
    monthEnd: Date,
    includeDetailedList: boolean,
  ): Promise<void> {
    try {
      console.log(`=== GENERATING MONTHLY REPORT ===`);
      console.log(`User: ${user.name} (${user.email})`);
      console.log(`Report Scope: ${reportScope}`);
      console.log(`Converted Leads: ${convertedLeads.length}`);
      console.log(`New Leads: ${newLeads.length}`);
      console.log(`Include Detailed List: ${includeDetailedList}`);

      // Generate PDF
      console.log(`Generating PDF...`);
      const pdfContent = this.generatePdfContent(
        convertedLeads,
        newLeads,
        reportScope,
        monthStart,
        monthEnd,
        includeDetailedList,
      );
      const pdfBuffer = await this.generatePdfBuffer(pdfContent);
      console.log(
        `PDF generated successfully, size: ${pdfBuffer.length} bytes`,
      );

      // Format date range
      const dateRange = `${monthStart.toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric',
      })} (${monthStart.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      })} - ${monthEnd.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      })})`;

      // Send email with PDF attachment
      console.log(`Sending email to ${user.email}...`);
      const subject = `Monthly Converted Leads Report - ${reportScope} (${dateRange})`;
      const message = `Hello ${user.name},\n\nPlease find attached the monthly converted leads report for ${reportScope}.\n\nSummary:\n- New Leads: ${newLeads.length}\n- Converted Leads: ${convertedLeads.length}\n- Conversion Rate: ${newLeads.length > 0 ? ((convertedLeads.length / newLeads.length) * 100).toFixed(2) : '0.00'}%\n\nRegards,\nKB Tracker`;

      // TEMPORARY: Test with simple email first
      await this.emailService.sendEmail(user.email, subject, 'generic-email', {
        title: subject,
        recipientName: user.name,
        message,
        appName: 'KB Tracker',
        timestamp: new Date().toLocaleString(),
      });

      console.log(`✅ Email sent successfully to ${user.email}`);
      this.logger.log(
        `Successfully sent monthly converted leads report to ${user.email}`,
      );

      // Create notification
      const notificationMessage = includeDetailedList
        ? `Your branch has ${convertedLeads.length} converted leads this month. Check email for full report.`
        : `Your ${reportScope.toLowerCase()} has ${convertedLeads.length} converted leads this month. Check email for full report.`;

      await this.notificationUtils.createNotification({
        type: NotificationType.CUSTOM,
        title: 'Monthly Converted Leads Report',
        message: notificationMessage,
        recipient_id: user.id,
        priority: 'NORMAL' as any,
        data: {
          reportType: 'monthly-converted-leads',
          convertedCount: convertedLeads.length,
          newLeadsCount: newLeads.length,
          reportScope,
          dateRange: `${monthStart.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}`,
        },
      });
    } catch (error) {
      console.error(
        `❌ CRITICAL ERROR in generateAndSendReport for ${user.email}:`,
        error,
      );
      this.logger.error(
        `Failed to send monthly converted leads report to ${user.email}:`,
        error,
      );
      throw error;
    }
  }

  private generatePdfBuffer(docDefinition: any): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        if (!pdfMake || typeof pdfMake.createPdf !== 'function') {
          throw new Error('pdfMake is not properly initialized');
        }

        const pdfDoc = pdfMake.createPdf(docDefinition);

        if (!pdfDoc || typeof pdfDoc.getBuffer !== 'function') {
          throw new Error('Failed to create PDF document');
        }

        pdfDoc.getBuffer((buffer: Buffer) => {
          if (!buffer) {
            reject(new Error('Failed to generate PDF buffer'));
            return;
          }
          resolve(buffer);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  private generatePdfContent(
    convertedLeads: ConvertedLead[],
    newLeads: NewLead[],
    reportScope: string,
    monthStart: Date,
    monthEnd: Date,
    includeDetailedList: boolean,
  ): any {
    const today = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    const content: any[] = [
      // Header
      {
        text: 'Monthly Converted Leads Report',
        style: 'header',
        alignment: 'center',
      },
      {
        text: `Report Period: ${monthStart.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}`,
        style: 'subheader',
        alignment: 'center',
      },
      {
        text: `Scope: ${reportScope}`,
        style: 'subheader',
        alignment: 'center',
      },
      { text: '', margin: [0, 20] }, // Spacer

      // Summary
      {
        text: 'Summary',
        style: 'sectionHeader',
      },
      {
        text: `New Leads Collected: ${newLeads.length}`,
        style: 'normal',
      },
      {
        text: `Converted Leads: ${convertedLeads.length}`,
        style: 'normal',
      },
      {
        text: `Conversion Rate: ${
          newLeads.length > 0
            ? ((convertedLeads.length / newLeads.length) * 100).toFixed(2)
            : '0.00'
        }%`,
        style: 'normal',
        margin: [0, 0, 0, 20],
      },
    ];

    // Add detailed lists if requested
    if (includeDetailedList) {
      if (convertedLeads.length > 0) {
        content.push(
          {
            text: 'Converted Leads Details',
            style: 'sectionHeader',
          },
          {
            table: {
              headerRows: 1,
              widths: ['*', '*', '*', '*'],
              body: [
                [
                  'Customer Name',
                  'Account Number',
                  'Branch',
                  'Conversion Date',
                ],
                ...convertedLeads.map((lead) => [
                  lead.customer_name,
                  lead.account_number,
                  lead.branch?.name || 'N/A',
                  lead.account_number_assigned_at?.toLocaleDateString() ||
                    'N/A',
                ]),
              ],
            },
            style: 'table',
            margin: [0, 0, 0, 20],
          },
        );
      }

      if (newLeads.length > 0) {
        content.push(
          {
            text: 'New Leads Details',
            style: 'sectionHeader',
          },
          {
            table: {
              headerRows: 1,
              widths: ['*', '*', '*', '*'],
              body: [
                ['Customer Name', 'Phone Number', 'Branch', 'Created Date'],
                ...newLeads.map((lead) => [
                  lead.customer_name,
                  'N/A', // Phone number not available in NewLead type
                  lead.branch?.name || 'N/A',
                  lead.created_at.toLocaleDateString(),
                ]),
              ],
            },
            style: 'table',
            margin: [0, 0, 0, 20],
          },
        );
      }
    }

    content.push(
      { text: '', margin: [0, 20] }, // Spacer
      {
        text: `Generated on: ${today}`,
        style: 'footer',
        alignment: 'right',
      },
    );

    return {
      content,
      styles: {
        header: {
          fontSize: 18,
          bold: true,
          margin: [0, 0, 0, 10],
        },
        subheader: {
          fontSize: 14,
          bold: true,
          margin: [0, 0, 0, 5],
        },
        sectionHeader: {
          fontSize: 14,
          bold: true,
          margin: [0, 10, 0, 5],
        },
        normal: {
          fontSize: 12,
          margin: [0, 0, 0, 5],
        },
        table: {
          fontSize: 10,
        },
        footer: {
          fontSize: 10,
          italics: true,
        },
      },
    };
  }
}
