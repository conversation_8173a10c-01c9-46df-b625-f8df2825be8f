import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';
import { PrismaService } from '../../prisma/prisma.service';
import { NotificationUtils } from '../../notifications/utils/notification.utils';
import { NotificationType } from '@prisma/client';
import { isApplicable } from '../../common/utils/date.utils';

export interface Overdue2by2by2NotificationsPayload {
  // Empty payload - uses current date
}

interface Overdue2by2by2Phase {
  id: string;
  type: string;
  execution_date: Date;
  customer_service_hitlist_record: {
    customer_name: string;
  };
  assigned_to: {
    id: string;
    name: string;
  };
}

@Injectable()
export class Overdue2by2by2NotificationsHandler implements TaskHandler {
  private readonly logger = new Logger(Overdue2by2by2NotificationsHandler.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly notificationUtils: NotificationUtils,
  ) {}

  getTaskType(): string {
    return 'overdue-2by2by2-notifications';
  }

  getDescription(): string {
    return 'Sends individual notifications to users with overdue 2by2by2 activities';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    return { isValid: true, errors: [] };
  }

  async handle(
    payload: Overdue2by2by2NotificationsPayload,
    job: Job,
  ): Promise<any> {
    this.logger.log('Processing overdue 2by2by2 notifications');

    let notificationsSent = 0;
    let overdueCount = 0;
    let usersProcessed = 0;

    try {
      await job.updateProgress(5);

      // Check if today is applicable for sending notifications (not weekend or holiday)
      const today = new Date();
      const applicable = await isApplicable(today, this.prisma);

      if (!applicable) {
        this.logger.log(
          'Skipping overdue 2by2by2 notifications - today is a weekend or holiday',
        );
        return {
          success: true,
          overdueCount: 0,
          notificationsSent: 0,
          usersProcessed: 0,
          skipped: true,
          reason: 'Weekend or holiday',
          processedAt: new Date().toISOString(),
        };
      }

      // Get today's date (DATE ONLY, no time)
      today.setHours(0, 0, 0, 0);

      // Get overdue 2by2by2 phases
      const overduePhases = (await this.prisma.twoByTwoPhase.findMany({
        where: {
          execution_date: {
            lt: today,
          },
          is_completed: false,
        },
        include: {
          customer_service_hitlist_record: {
            select: {
              customer_name: true,
            },
          },
          assigned_to: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: [
          { assigned_to: { name: 'asc' } },
          { execution_date: 'asc' },
        ],
      })) as any[];

      await job.updateProgress(30);

      if (overduePhases.length === 0) {
        this.logger.log('No overdue 2by2by2 phases found');
        return {
          success: true,
          overdueCount: 0,
          notificationsSent: 0,
          usersProcessed: 0,
          processedAt: new Date().toISOString(),
        };
      }

      overdueCount = overduePhases.length;
      this.logger.log(`Found ${overdueCount} overdue 2by2by2 phases`);

      // Group phases by user
      const phasesByUser = new Map<string, Overdue2by2by2Phase[]>();
      
      overduePhases.forEach((phase) => {
        const userId = phase.assigned_to.id;
        if (!phasesByUser.has(userId)) {
          phasesByUser.set(userId, []);
        }
        phasesByUser.get(userId)!.push(phase);
      });

      await job.updateProgress(50);

      const progressStep = 40 / phasesByUser.size;
      let currentProgress = 50;

      // Send notifications to each user
      for (const [userId, userPhases] of phasesByUser) {
        try {
          const userName = userPhases[0].assigned_to.name;
          
          // Create notification message
          let message: string;
          if (userPhases.length === 1) {
            const phase = userPhases[0];
            const phaseDisplayName = this.getPhaseDisplayName(phase.type);
            message = `You have an overdue ${phaseDisplayName} activity for ${phase.customer_service_hitlist_record.customer_name}. Visit the overdue tab to see details.`;
          } else {
            message = `You have ${userPhases.length} overdue 2by2by2 activities. Visit the overdue tab to see details.`;
          }

          // Create notification (no email, just notification)
          await this.notificationUtils.createNotification({
            type: NotificationType.CUSTOM,
            title: 'Overdue 2by2by2 Activities',
            message,
            recipient_id: userId,
            priority: 'HIGH' as any,
            data: {
              notificationType: 'overdue-2by2by2',
              overdueCount: userPhases.length,
              phases: userPhases.map(phase => ({
                id: phase.id,
                type: phase.type,
                customerName: phase.customer_service_hitlist_record.customer_name,
                executionDate: phase.execution_date.toISOString(),
              })),
            },
          });

          notificationsSent++;
          usersProcessed++;

          this.logger.log(
            `Sent overdue 2by2by2 notification to ${userName} (${userPhases.length} activities)`,
          );

          currentProgress += progressStep;
          await job.updateProgress(Math.min(currentProgress, 90));
        } catch (error) {
          this.logger.error(
            `Failed to send overdue 2by2by2 notification to user ${userId}:`,
            error,
          );
        }
      }

      await job.updateProgress(100);

      this.logger.log(
        `Overdue 2by2by2 notifications processing completed. Notifications sent: ${notificationsSent}, Users processed: ${usersProcessed}`,
      );

      return {
        success: true,
        overdueCount,
        notificationsSent,
        usersProcessed,
        processedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to process overdue 2by2by2 notifications:', error);
      throw error;
    }
  }

  private getPhaseDisplayName(type: string): string {
    switch (type) {
      case 'first2':
        return 'First 2';
      case 'second2':
        return 'Second 2';
      case 'third2':
        return 'Third 2';
      default:
        return type;
    }
  }
}
