import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';
import { PrismaService } from '../../prisma/prisma.service';
import { EmailService } from '../../common/services/email.service';
import { isApplicable } from '../../common/utils/date.utils';
import { NotificationUtils } from '../../notifications/utils/notification.utils';
import { NotificationType } from '@prisma/client';
// Initialize pdfmake
const pdfMake = require('pdfmake/build/pdfmake');
const pdfFonts = require('pdfmake/build/vfs_fonts');

// Set fonts for pdfmake (pdfFonts contains the font data directly)
pdfMake.vfs = pdfFonts;

export interface WeeklyConvertedLeadsReportsPayload {
  // Empty payload - uses current date
}

interface ConvertedLead {
  id: string;
  customer_name: string | null;
  account_number: string | null;
  account_number_assigned_at: Date | null;
  branch: {
    id: string;
    name: string;
    region: {
      id: string;
      name: string;
    };
  } | null;
  account_number_assigned_by_user: {
    id: string;
    name: string;
  } | null;
}

interface NewLead {
  id: string;
  customer_name: string | null;
  created_at: Date;
  branch: {
    id: string;
    name: string;
    region: {
      id: string;
      name: string;
    };
  } | null;
  rm_user: {
    id: string;
    name: string;
  } | null;
}

interface UserWithPermissions {
  id: string;
  name: string;
  email: string;
  branch_id: string;
  branch: {
    id: string;
    name: string;
    region: {
      id: string;
      name: string;
    };
  };
  permissions: Array<{
    id: string;
  }>;
}

@Injectable()
export class WeeklyConvertedLeadsReportsHandler implements TaskHandler {
  private readonly logger = new Logger(WeeklyConvertedLeadsReportsHandler.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly emailService: EmailService,
    private readonly notificationUtils: NotificationUtils,
  ) {}

  getTaskType(): string {
    return 'weekly-converted-leads-reports';
  }

  getDescription(): string {
    return 'Sends weekly converted leads reports to users with appropriate permissions';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    return { isValid: true, errors: [] };
  }

  async handle(
    payload: WeeklyConvertedLeadsReportsPayload,
    job: Job,
  ): Promise<any> {
    this.logger.log('Processing weekly converted leads reports');

    try {
      await job.updateProgress(5);

      // Check if today is applicable for sending reports (not weekend or holiday)
      const today = new Date();
      const applicable = await isApplicable(today, this.prisma);

      if (!applicable) {
        this.logger.log(
          'Skipping weekly converted leads reports - today is a weekend or holiday',
        );
        return {
          success: true,
          convertedCount: 0,
          newLeadsCount: 0,
          reportsSent: 0,
          usersProcessed: 0,
          skipped: true,
          reason: 'Weekend or holiday',
          processedAt: new Date().toISOString(),
        };
      }

      // Calculate week start (Monday) and end (current date)
      const now = new Date();
      const weekStart = new Date(now);
      const dayOfWeek = weekStart.getDay();
      const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Sunday = 0, Monday = 1
      weekStart.setDate(weekStart.getDate() - daysToMonday);
      weekStart.setHours(0, 0, 0, 0);

      const weekEnd = new Date(now);
      weekEnd.setHours(23, 59, 59, 999);

      this.logger.log(
        `Processing weekly report from ${weekStart.toISOString()} to ${weekEnd.toISOString()}`,
      );

      await job.updateProgress(10);

      // Get converted leads for the week
      const convertedLeads = (await this.prisma.lead.findMany({
        where: {
          account_number: { not: null },
          account_number_assigned_at: {
            gte: weekStart,
            lte: weekEnd,
          },
        },
        include: {
          branch: {
            include: {
              region: true,
            },
          },
          account_number_assigned_by_user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: [
          { branch: { name: 'asc' } },
          { account_number_assigned_at: 'desc' },
        ],
      })) as any[];

      await job.updateProgress(20);

      // Get new leads for the week
      const newLeads = (await this.prisma.lead.findMany({
        where: {
          created_at: {
            gte: weekStart,
            lte: weekEnd,
          },
        },
        include: {
          branch: {
            include: {
              region: true,
            },
          },
          rm_user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: [{ branch: { name: 'asc' } }, { created_at: 'desc' }],
      })) as any[];

      await job.updateProgress(30);

      if (convertedLeads.length === 0 && newLeads.length === 0) {
        this.logger.log('No converted leads or new leads found for this week');
        return {
          success: true,
          convertedCount: 0,
          newLeadsCount: 0,
          reportsSent: 0,
          usersProcessed: 0,
          processedAt: new Date().toISOString(),
        };
      }

      this.logger.log(
        `Found ${convertedLeads.length} converted leads and ${newLeads.length} new leads for this week`,
      );

      // Get users with weekly converted leads report permissions
      const usersWithPermissions = (await this.prisma.user.findMany({
        where: {
          role: {
            role_permissions: {
              some: {
                permission: {
                  id: {
                    in: [
                      'reports.converted.leads.all.weekly',
                      'reports.converted.leads.region.weekly',
                      'reports.converted.leads.branch.weekly',
                    ],
                  },
                },
              },
            },
          },
        },
        include: {
          branch: {
            include: {
              region: true,
            },
          },
          role: {
            include: {
              role_permissions: {
                include: {
                  permission: {
                    select: {
                      id: true,
                    },
                  },
                },
              },
            },
          },
        },
      })) as any[];

      await job.updateProgress(35);

      if (usersWithPermissions.length === 0) {
        this.logger.log(
          'No users found with weekly converted leads report permissions',
        );
        return {
          success: true,
          convertedCount: convertedLeads.length,
          newLeadsCount: newLeads.length,
          reportsSent: 0,
          usersProcessed: 0,
          processedAt: new Date().toISOString(),
        };
      }

      this.logger.log(
        `Found ${usersWithPermissions.length} users with weekly converted leads report permissions`,
      );

      let reportsSent = 0;
      const progressStep = 60 / usersWithPermissions.length;

      // Process each user with permissions
      for (const user of usersWithPermissions) {
        try {
          const userPermissions = user.role.role_permissions.map(
            (rp: any) => rp.permission.id,
          );

          let reportScope = '';
          let filteredConvertedLeads: ConvertedLead[] = [];
          let filteredNewLeads: NewLead[] = [];

          // Apply permission hierarchy: all > region > branch
          if (userPermissions.includes('reports.converted.leads.all.weekly')) {
            reportScope = 'All Branches';
            filteredConvertedLeads = convertedLeads;
            filteredNewLeads = newLeads;
          } else if (
            userPermissions.includes('reports.converted.leads.region.weekly')
          ) {
            reportScope = `${user.branch.region.name}`;
            filteredConvertedLeads = convertedLeads.filter(
              (lead) => lead.branch.region.id === user.branch.region.id,
            );
            filteredNewLeads = newLeads.filter(
              (lead) => lead.branch.region.id === user.branch.region.id,
            );
          } else if (
            userPermissions.includes('reports.converted.leads.branch.weekly')
          ) {
            reportScope = `${user.branch.name}`;
            filteredConvertedLeads = convertedLeads.filter(
              (lead) => lead.branch.id === user.branch.id,
            );
            filteredNewLeads = newLeads.filter(
              (lead) => lead.branch.id === user.branch.id,
            );
          }

          console.log(`=== WEEKLY CONVERTED LEADS DEBUG ===`);
          console.log(`User: ${user.name} (${user.email})`);
          console.log(`Branch: ${user.branch.name} (${user.branch.id})`);
          console.log(
            `Region: ${user.branch.region.name} (${user.branch.region.id})`,
          );
          console.log(`Report Scope: ${reportScope}`);
          console.log(
            `User Permissions: ${userPermissions.filter((p: any) => p.includes('converted.leads')).join(', ')}`,
          );
          console.log(
            `Filtered Converted Leads: ${filteredConvertedLeads.length}`,
          );
          console.log(`Filtered New Leads: ${filteredNewLeads.length}`);

          if (
            filteredConvertedLeads.length > 0 ||
            filteredNewLeads.length > 0
          ) {
            console.log(
              `✅ Sending weekly report to ${user.name} for ${reportScope}`,
            );
            await this.generateAndSendReport(
              user,
              filteredConvertedLeads,
              filteredNewLeads,
              reportScope,
              weekStart,
              weekEnd,
              userPermissions.includes('reports.converted.leads.branch.weekly'),
            );
            reportsSent++;
            console.log(`✅ Weekly report sent successfully to ${user.name}`);
          } else {
            console.log(`⚠️  No data to send weekly report to ${user.name}`);
          }
        } catch (error) {
          console.error(
            `❌ ERROR generating weekly report for user ${user.name} (${user.email}):`,
            error,
          );
          this.logger.error(
            `Failed to generate weekly report for user ${user.id}:`,
            error,
          );
        }

        await job.updateProgress(35 + reportsSent * progressStep);
      }

      await job.updateProgress(100);

      this.logger.log(
        `Weekly converted leads reports task completed. Sent ${reportsSent} reports`,
      );

      return {
        success: true,
        convertedCount: convertedLeads.length,
        newLeadsCount: newLeads.length,
        reportsSent,
        usersProcessed: usersWithPermissions.length,
        processedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        'Failed to process weekly converted leads reports:',
        error,
      );
      throw error;
    }
  }

  private async generateAndSendReport(
    user: UserWithPermissions,
    convertedLeads: ConvertedLead[],
    newLeads: NewLead[],
    reportScope: string,
    weekStart: Date,
    weekEnd: Date,
    includeDetailedList: boolean,
  ): Promise<void> {
    try {
      // Generate PDF
      const pdfBuffer = await this.generatePDF(
        convertedLeads,
        newLeads,
        reportScope,
        weekStart,
        weekEnd,
        includeDetailedList,
      );

      // Format date range
      const dateRange = `${weekStart.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      })} - ${weekEnd.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      })}`;

      // Send email with PDF attachment
      const subject = `Weekly Converted Leads Report - ${reportScope} (${dateRange})`;
      const message = `Hello ${user.name},\n\nPlease find attached the weekly converted leads report for ${reportScope}.\n\nSummary:\n- New Leads: ${newLeads.length}\n- Converted Leads: ${convertedLeads.length}\n- Conversion Rate: ${newLeads.length > 0 ? ((convertedLeads.length / newLeads.length) * 100).toFixed(2) : '0.00'}%\n\nRegards,\nKB Tracker`;

      await this.emailService.sendEmailWithAttachments(
        user.email,
        subject,
        'generic-email',
        {
          title: subject,
          recipientName: user.name,
          message,
          appName: 'KB Tracker',
          timestamp: new Date().toLocaleString(),
        },
        [
          {
            filename: `weekly-converted-leads-${reportScope.toLowerCase().replace(/\s+/g, '-')}-${weekStart.getFullYear()}-W${Math.ceil(weekStart.getDate() / 7)}.pdf`,
            content: pdfBuffer,
            contentType: 'application/pdf',
          },
        ],
      );

      this.logger.log(
        `Successfully sent weekly converted leads report to ${user.email}`,
      );

      // Create notification
      const notificationMessage = includeDetailedList
        ? `Your branch has ${convertedLeads.length} converted leads this week. Check email for full report.`
        : `Your ${reportScope.toLowerCase()} has ${convertedLeads.length} converted leads this week. Check email for full report.`;

      await this.notificationUtils.createNotification({
        type: NotificationType.CUSTOM,
        title: 'Weekly Converted Leads Report',
        message: notificationMessage,
        recipient_id: user.id,
        priority: 'NORMAL' as any,
        data: {
          reportType: 'weekly-converted-leads',
          convertedCount: convertedLeads.length,
          newLeadsCount: newLeads.length,
          reportScope,
          dateRange,
        },
      });
    } catch (error) {
      this.logger.error(
        `Failed to send weekly converted leads report to ${user.email}:`,
        error,
      );
      throw error;
    }
  }

  private async generatePDF(
    convertedLeads: ConvertedLead[],
    newLeads: NewLead[],
    reportScope: string,
    weekStart: Date,
    weekEnd: Date,
    includeDetailedList: boolean,
  ): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        const docDefinition = {
          content: [
            // Header
            {
              text: 'Weekly Converted Leads Report',
              style: 'header',
              alignment: 'center',
            },
            {
              text: `Report Period: ${weekStart.toLocaleDateString()} - ${weekEnd.toLocaleDateString()}`,
              style: 'subheader',
              alignment: 'center',
            },
            {
              text: `Scope: ${reportScope}`,
              style: 'subheader',
              alignment: 'center',
            },
            { text: '', margin: [0, 20] }, // Spacer

            // Summary
            {
              text: 'Summary',
              style: 'sectionHeader',
            },
            {
              text: `New Leads Collected: ${newLeads.length}`,
              style: 'normal',
            },
            {
              text: `Converted Leads: ${convertedLeads.length}`,
              style: 'normal',
            },

            // Content based on report type
            ...(includeDetailedList
              ? this.getDetailedContent(convertedLeads, newLeads)
              : this.getSummaryContent(convertedLeads, newLeads)),

            // Footer
            {
              text: `Generated on ${new Date().toLocaleString()}`,
              style: 'footer',
              alignment: 'center',
              absolutePosition: { x: 0, y: 750 },
            },
          ],
          styles: {
            header: {
              fontSize: 20,
              bold: true,
              margin: [0, 0, 0, 10],
            },
            subheader: {
              fontSize: 14,
              margin: [0, 0, 0, 5],
            },
            sectionHeader: {
              fontSize: 14,
              bold: true,
              margin: [0, 10, 0, 5],
            },
            normal: {
              fontSize: 10,
              margin: [0, 2],
            },
            footer: {
              fontSize: 8,
              margin: [0, 10],
            },
          },
        };

        if (!pdfMake || typeof pdfMake.createPdf !== 'function') {
          throw new Error('pdfMake is not properly initialized');
        }

        const pdfDoc = pdfMake.createPdf(docDefinition);

        if (!pdfDoc || typeof pdfDoc.getBuffer !== 'function') {
          throw new Error('Failed to create PDF document');
        }

        pdfDoc.getBuffer((buffer: Buffer) => {
          if (!buffer) {
            reject(new Error('Failed to generate PDF buffer'));
          } else {
            resolve(buffer);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  private getDetailedContent(
    convertedLeads: ConvertedLead[],
    newLeads: NewLead[],
  ): any[] {
    const content: any[] = [];

    if (newLeads.length > 0) {
      content.push(
        { text: '', margin: [0, 20] }, // Spacer
        {
          text: 'New Leads Details',
          style: 'sectionHeader',
        },
      );

      newLeads.forEach((lead, index) => {
        content.push({
          text: `${index + 1}. ${lead.customer_name} - Created: ${lead.created_at.toLocaleDateString()} (${lead.rm_user?.name || 'Unknown'})`,
          style: 'normal',
        });
      });
    }

    if (convertedLeads.length > 0) {
      content.push(
        { text: '', margin: [0, 20] }, // Spacer
        {
          text: 'Converted Leads Details',
          style: 'sectionHeader',
        },
      );

      convertedLeads.forEach((lead, index) => {
        content.push({
          text: `${index + 1}. ${lead.customer_name || 'Unknown'} - Account: ${lead.account_number || 'N/A'} - Converted: ${lead.account_number_assigned_at?.toLocaleDateString() || 'N/A'} (${lead.account_number_assigned_by_user?.name || 'Unknown'})`,
          style: 'normal',
        });
      });
    }

    return content;
  }

  private getSummaryContent(
    convertedLeads: ConvertedLead[],
    newLeads: NewLead[],
  ): any[] {
    const branchStats = this.calculateBranchStats(convertedLeads, newLeads);
    const content: any[] = [];

    if (Object.keys(branchStats).length > 0) {
      content.push(
        { text: '', margin: [0, 20] }, // Spacer
        {
          text: 'Branch Breakdown',
          style: 'sectionHeader',
        },
      );

      Object.entries(branchStats).forEach(([branchName, stats]) => {
        content.push({
          text: `${branchName}: ${stats.newLeads} new, ${stats.converted} converted`,
          style: 'normal',
        });
      });
    }

    return content;
  }

  private calculateBranchStats(
    convertedLeads: ConvertedLead[],
    newLeads: NewLead[],
  ) {
    const stats: Record<string, { newLeads: number; converted: number }> = {};

    // Count new leads by branch
    newLeads.forEach((lead) => {
      const branchName = lead.branch?.name || 'Unknown Branch';
      if (!stats[branchName]) {
        stats[branchName] = { newLeads: 0, converted: 0 };
      }
      stats[branchName].newLeads++;
    });

    // Count converted leads by branch
    convertedLeads.forEach((lead) => {
      const branchName = lead.branch?.name || 'Unknown Branch';
      if (!stats[branchName]) {
        stats[branchName] = { newLeads: 0, converted: 0 };
      }
      stats[branchName].converted++;
    });

    return stats;
  }
}
