import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { TaskHandler } from './task-handler.interface';
import { PrismaService } from '../../prisma/prisma.service';
import { EmailService } from '../../common/services/email.service';
import { isApplicable } from '../../common/utils/date.utils';
import { NotificationUtils } from '../../notifications/utils/notification.utils';
import { NotificationType } from '@prisma/client';
// Initialize pdfmake
const pdfMake = require('pdfmake/build/pdfmake');
const pdfFonts = require('pdfmake/build/vfs_fonts');

// Set fonts for pdfmake (pdfFonts contains the font data directly)
pdfMake.vfs = pdfFonts;

export interface OverdueReportsPayload {
  // Empty payload - uses current date
}

interface OverdueFollowUp {
  id: string;
  for_date: Date;
  type: string;
  lead: {
    customer_name: string | null;
  };
  user: {
    id: string;
    name: string;
  };
  branch: {
    id: string;
    name: string;
    region: {
      id: string;
      name: string;
    };
  };
}

@Injectable()
export class OverdueReportsHandler implements TaskHandler {
  private readonly logger = new Logger(OverdueReportsHandler.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly emailService: EmailService,
    private readonly notificationUtils: NotificationUtils,
  ) {}

  getTaskType(): string {
    return 'overdue-reports';
  }

  getDescription(): string {
    return 'Generates and sends PDF reports of overdue follow-ups to users based on their permissions';
  }

  validatePayload(payload: any): { isValid: boolean; errors: string[] } {
    return { isValid: true, errors: [] };
  }

  async handle(payload: OverdueReportsPayload, job: Job): Promise<any> {
    this.logger.log('Processing overdue reports generation and distribution');

    try {
      await job.updateProgress(5);

      // Check if today is applicable for sending reports (not weekend or holiday)
      const today = new Date();
      const applicable = await isApplicable(today, this.prisma);

      if (!applicable) {
        this.logger.log(
          'Skipping overdue reports - today is a weekend or holiday',
        );

        return {
          success: true,
          overdueCount: 0,
          reportsSent: 0,
          usersProcessed: 0,
          skipped: true,
          reason: 'Weekend or holiday',
          processedAt: new Date().toISOString(),
        };
      }

      // Find all overdue follow-ups (date_completed is null and for_date is before current time)
      const now = new Date();

      const overdueFollowUps = (await this.prisma.followUp.findMany({
        where: {
          date_completed: null,
          canceled_at: null,
          for_date: {
            lt: now, // Before current time (not just today)
          },
        },
        include: {
          lead: {
            select: {
              customer_name: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
            },
          },
          branch: {
            select: {
              id: true,
              name: true,
              region: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
        orderBy: [
          { branch: { name: 'asc' } },
          { user: { name: 'asc' } },
          { for_date: 'desc' },
        ],
      })) as OverdueFollowUp[];

      await job.updateProgress(15);

      if (overdueFollowUps.length === 0) {
        this.logger.log('No overdue follow-ups found for today');
        return { success: true, overdueCount: 0, reportsSent: 0 };
      }

      this.logger.log(`Found ${overdueFollowUps.length} overdue follow-ups`);

      await job.updateProgress(25);

      // Get users with report permissions
      const usersWithPermissions = await this.prisma.user.findMany({
        where: {
          role: {
            role_permissions: {
              some: {
                permission: {
                  id: {
                    in: [
                      'reports.overdue.followups.all',
                      'reports.overdue.followups.branch',
                      'reports.overdue.followups.region',
                    ],
                  },
                },
              },
            },
          },
        },
        include: {
          branch: {
            include: {
              region: true,
            },
          },
          role: {
            include: {
              role_permissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      });

      await job.updateProgress(35);

      let reportsSent = 0;
      const progressStep = 60 / usersWithPermissions.length;

      for (const user of usersWithPermissions) {
        try {
          const userPermissions = user.role.role_permissions.map(
            (rp) => rp.permission.id,
          );

          let filteredFollowUps: OverdueFollowUp[] = [];
          let reportScope = '';

          if (userPermissions.includes('reports.overdue.followups.all')) {
            filteredFollowUps = overdueFollowUps;
            reportScope = 'All Branches';
          } else if (
            userPermissions.includes('reports.overdue.followups.region')
          ) {
            filteredFollowUps = overdueFollowUps.filter(
              (fu) => fu.branch.region.id === user.branch.region.id,
            );
            reportScope = user.branch.region.name;
          } else if (
            userPermissions.includes('reports.overdue.followups.branch')
          ) {
            filteredFollowUps = overdueFollowUps.filter(
              (fu) => fu.branch.id === user.branch_id,
            );
            reportScope = user.branch.name;
          }

          if (filteredFollowUps.length > 0) {
            await this.generateAndSendReport(
              user,
              filteredFollowUps,
              reportScope,
            );
            reportsSent++;
          }
        } catch (error) {
          this.logger.error(
            `Failed to generate report for user ${user.id}:`,
            error,
          );
        }

        await job.updateProgress(35 + reportsSent * progressStep);
      }

      await job.updateProgress(100);

      this.logger.log(
        `Overdue reports task completed. Sent ${reportsSent} reports`,
      );

      return {
        success: true,
        overdueCount: overdueFollowUps.length,
        reportsSent,
        usersProcessed: usersWithPermissions.length,
        processedAt: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to process overdue reports:', error);
      throw error;
    }
  }

  private async generateAndSendReport(
    user: any,
    overdueFollowUps: OverdueFollowUp[],
    reportScope: string,
  ): Promise<void> {
    // Calculate statistics
    const totalOverdue = overdueFollowUps.length;
    const callsCount = overdueFollowUps.filter(
      (fu) => fu.type === 'call',
    ).length;
    const visitsCount = overdueFollowUps.filter(
      (fu) => fu.type === 'visit',
    ).length;

    // Group by branch for "all" and "region" reports, by agent for "branch" reports
    const isAllOrRegion =
      reportScope === 'All Branches' || reportScope !== user.branch.name;

    let groupedData: { [key: string]: OverdueFollowUp[] } = {};

    if (isAllOrRegion) {
      // Group by branch
      groupedData = overdueFollowUps.reduce(
        (acc, fu) => {
          const key = fu.branch.name;
          if (!acc[key]) acc[key] = [];
          acc[key].push(fu);
          return acc;
        },
        {} as { [key: string]: OverdueFollowUp[] },
      );
    } else {
      // Group by agent
      groupedData = overdueFollowUps.reduce(
        (acc, fu) => {
          const key = fu.user.name;
          if (!acc[key]) acc[key] = [];
          acc[key].push(fu);
          return acc;
        },
        {} as { [key: string]: OverdueFollowUp[] },
      );
    }

    // Generate PDF content
    const pdfContent = this.generatePdfContent(
      reportScope,
      totalOverdue,
      callsCount,
      visitsCount,
      groupedData,
      isAllOrRegion,
    );

    // Generate PDF buffer
    const pdfBuffer = await this.generatePdfBuffer(pdfContent);

    // Send email with PDF attachment
    await this.sendReportEmail(
      user,
      reportScope,
      totalOverdue,
      callsCount,
      visitsCount,
      pdfBuffer,
    );
  }

  private generatePdfContent(
    reportScope: string,
    totalOverdue: number,
    callsCount: number,
    visitsCount: number,
    groupedData: { [key: string]: OverdueFollowUp[] },
    isAllOrRegion: boolean,
  ): any {
    const today = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    // Create table data
    const tableBody: any[][] = [
      [
        'Lead Name',
        'Follow-Up Type',
        'Scheduled Date & Time',
        'Overdue By',
        'Assigned Agent',
        ...(isAllOrRegion ? ['Branch'] : []),
      ],
    ];

    // Add rows grouped by branch or agent
    Object.keys(groupedData).forEach((groupKey) => {
      // Add group header
      const headerRow: any[] = [
        {
          text: groupKey,
          style: 'groupHeader',
          colSpan: isAllOrRegion ? 6 : 5,
        },
      ];
      // Add empty cells for the remaining columns
      for (let i = 1; i < (isAllOrRegion ? 6 : 5); i++) {
        headerRow.push({});
      }
      tableBody.push(headerRow);

      // Add follow-ups for this group
      groupedData[groupKey].forEach((fu) => {
        const scheduledDate = new Date(fu.for_date);
        const now = new Date();
        const overdueDays = Math.floor(
          (now.getTime() - scheduledDate.getTime()) / (1000 * 60 * 60 * 24),
        );
        const overdueHours = Math.floor(
          ((now.getTime() - scheduledDate.getTime()) % (1000 * 60 * 60 * 24)) /
            (1000 * 60 * 60),
        );

        let overdueText = '';
        if (overdueDays > 0) {
          overdueText = `${overdueDays} day${overdueDays > 1 ? 's' : ''}`;
        } else if (overdueHours > 0) {
          overdueText = `${overdueHours} hour${overdueHours > 1 ? 's' : ''}`;
        } else {
          overdueText = 'Less than 1 hour';
        }

        tableBody.push([
          fu.lead.customer_name || 'Unknown Lead',
          fu.type === 'call' ? 'Call' : 'Visit',
          scheduledDate.toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          }),
          overdueText,
          fu.user.name,
          ...(isAllOrRegion ? [fu.branch.name] : []),
        ]);
      });
    });

    return {
      content: [
        // Header
        {
          text: 'Overdue Follow-Ups Report',
          style: 'header',
          alignment: 'center',
          margin: [0, 0, 0, 20],
        },
        {
          columns: [
            { text: `Branch: ${reportScope}`, style: 'subheader' },
            { text: `Date: ${today}`, style: 'subheader', alignment: 'right' },
          ],
          margin: [0, 0, 0, 20],
        },

        // Summary Section
        {
          text: 'Summary',
          style: 'sectionHeader',
          margin: [0, 0, 0, 10],
        },
        {
          ul: [
            `Total overdue follow-ups: ${totalOverdue}`,
            `Calls: ${callsCount}`,
            `Visits: ${visitsCount}`,
          ],
          margin: [0, 0, 0, 20],
        },

        // Table
        {
          table: {
            headerRows: 1,
            widths: isAllOrRegion
              ? ['*', 'auto', 'auto', 'auto', 'auto', 'auto']
              : ['*', 'auto', 'auto', 'auto', 'auto'],
            body: tableBody,
          },
          layout: 'lightHorizontalLines',
        },
      ],
      styles: {
        header: {
          fontSize: 18,
          bold: true,
        },
        subheader: {
          fontSize: 12,
          bold: true,
        },
        sectionHeader: {
          fontSize: 14,
          bold: true,
        },
        groupHeader: {
          fontSize: 12,
          bold: true,
          fillColor: '#f0f0f0',
        },
      },
      defaultStyle: {
        fontSize: 10,
      },
    };
  }

  private generatePdfBuffer(docDefinition: any): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        if (!pdfMake || typeof pdfMake.createPdf !== 'function') {
          throw new Error('pdfMake is not properly initialized');
        }

        const pdfDoc = pdfMake.createPdf(docDefinition);

        if (!pdfDoc || typeof pdfDoc.getBuffer !== 'function') {
          throw new Error('Failed to create PDF document');
        }

        pdfDoc.getBuffer((buffer: Buffer) => {
          if (!buffer) {
            reject(new Error('Failed to generate PDF buffer'));
          } else {
            resolve(buffer);
          }
        });
      } catch (error) {
        this.logger.error('PDF generation failed:', error);
        reject(error);
      }
    });
  }

  private async sendReportEmail(
    user: any,
    reportScope: string,
    totalOverdue: number,
    callsCount: number,
    visitsCount: number,
    pdfBuffer: Buffer,
  ): Promise<void> {
    const isAllOrRegion =
      reportScope === 'All Branches' || reportScope !== user.branch.name;

    let subject: string;
    let message: string;

    if (isAllOrRegion) {
      subject = `Overdue Follow-Ups Report - ${reportScope}`;
      message = `${reportScope} has ${totalOverdue} overdue follow-ups. ${callsCount} Calls, ${visitsCount} Visits. Please see attached report for details.`;
    } else {
      subject = `Overdue Follow-Ups Report - ${reportScope}`;
      message = `Hello ${user.name},\n\n${reportScope} currently has ${totalOverdue} overdue follow-ups:\n- ${callsCount} Calls\n- ${visitsCount} Visits\n\nPlease find the detailed report attached.\n\nRegards,\nKB Tracker`;
    }

    // Send email with PDF attachment
    await this.emailService.sendEmailWithAttachments(
      user.email,
      subject,
      'generic-email',
      {
        title: subject,
        recipientName: user.name,
        message,
        appName: 'KB Tracker',
        timestamp: new Date().toLocaleString(),
      },
      [
        {
          filename: `overdue-followups-${reportScope.toLowerCase().replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf',
        },
      ],
    );

    this.logger.log(`Sent overdue report to ${user.email} for ${reportScope}`);

    // Create notification
    const notificationMessage = `${totalOverdue} overdue follow-ups found (${callsCount} calls, ${visitsCount} visits). Check email for full report.`;

    await this.notificationUtils.createNotification({
      type: NotificationType.CUSTOM,
      title: 'Overdue Follow-Ups Report',
      message: notificationMessage,
      recipient_id: user.id,
      priority: 'HIGH' as any,
      data: {
        reportType: 'overdue-followups',
        totalOverdue,
        callsCount,
        visitsCount,
        reportScope,
      },
    });
  }
}
