import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { FileStorageService } from '../common/services/file-storage.service';
import { NotificationTriggerService } from '../notifications/notification-trigger.service';
import { BackgroundTaskService } from '../scheduled-tasks/background-task.service';
import { CreateCallActivityDto } from './dto/create-call-activity.dto';
import { CreateVisitActivityDto } from './dto/create-visit-activity.dto';
import { CallActivityResponseDto } from './dto/call-activity-response.dto';
import { VisitActivityResponseDto } from './dto/visit-activity-response.dto';
import {
  FollowupsListResponseDto,
  FollowupResponseDto,
} from './dto/followup-response.dto';
import {
  UpdateFollowupStatusDto,
  UpdateFollowupStatusResponseDto,
} from './dto/update-followup-status.dto';
import { TargetUpdateService } from '../targets/services/target-update.service';

/**
 * Service handling all activity-related business logic
 * Provides operations for creating and managing activities
 */
@Injectable()
export class ActivitiesService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly fileStorageService: FileStorageService,
    private readonly targetUpdateService: TargetUpdateService,
    private readonly notificationTriggerService: NotificationTriggerService,
    private readonly backgroundTaskService: BackgroundTaskService,
  ) {}

  /**
   * Creates a new call activity with attachments
   * @param createCallActivityDto - Data for creating the call activity
   * @returns Promise<CallActivityResponseDto> - The created call activity
   */
  async createCallActivity(
    createCallActivityDto: CreateCallActivityDto,
  ): Promise<CallActivityResponseDto> {
    const {
      call_type,
      call_status,
      notes,
      follow_up_date,
      purpose_id,
      leadID,
      performed_by_user_id,
      attachments = [],
    } = createCallActivityDto;

    // Use the provided leadID directly
    const leadId = leadID;
    let performedByUserId = performed_by_user_id;

    if (!performedByUserId) {
      const firstUser = await this.prisma.user.findFirst();
      if (!firstUser) {
        throw new BadRequestException(
          'No users found in the system. Please provide a performed_by_user_id.',
        );
      }
      performedByUserId = firstUser.id;
    }

    // Validate that the lead exists
    const lead = await this.prisma.lead.findUnique({
      where: { id: leadId },
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${leadId}' not found`);
    }

    // Validate that the purpose exists
    const purpose = await this.prisma.purposeOfActivity.findUnique({
      where: { id: purpose_id },
    });

    if (!purpose) {
      throw new NotFoundException(`Purpose with ID '${purpose_id}' not found`);
    }

    // Validate that the user exists
    const user = await this.prisma.user.findUnique({
      where: { id: performedByUserId },
    });

    if (!user) {
      throw new NotFoundException(
        `User with ID '${performedByUserId}' not found`,
      );
    }

    try {
      // Create the activity with attachments in a transaction
      const activity = await this.prisma.$transaction(async (tx) => {
        // Create the activity
        const newActivity = await tx.activity.create({
          data: {
            lead_id: leadId,
            activity_type: call_type, // Store call_type in activity_type field
            interaction_type: 'call', // Set interaction_type to 'call'
            call_status: call_status,
            notes: notes || null,
            next_followup_date: follow_up_date
              ? new Date(follow_up_date)
              : null,
            purpose_id: purpose_id,
            performed_by_user_id: performedByUserId,
            branch_id: user.branch_id, // Record the branch
          },
        });

        // Create attachments if provided
        if (attachments && attachments.length > 0) {
          // Save files to disk and create attachment records
          const savedFiles = await this.fileStorageService.saveFiles(
            attachments.map((att) => att.file),
            `activities/${newActivity.id}`,
          );

          // Create attachment records with actual file URLs
          await tx.activityAttachment.createMany({
            data: savedFiles.map((savedFile) => ({
              activity_id: newActivity.id,
              file_url: savedFile.url,
            })),
          });
        }

        // Create follow-up if follow_up_date is provided
        let createdFollowUp: any = null;
        if (follow_up_date) {
          createdFollowUp = await tx.followUp.create({
            data: {
              parent_activity_id: newActivity.id,
              branch_id: user.branch_id,
              user_id: performedByUserId,
              for_date: new Date(follow_up_date),
              lead_id: leadId,
              type: 'call', // Use the interaction_type from the activity
            },
          });
        }

        // UPDATE THE USER TARGETS IF CALL WAS SUCCESSFUL
        if (call_status.toLowerCase() === 'success') {
          let targetActivity: string = 'LEADS';

          // CALL THE TARGET UPDATE FUNCTION
          await this.targetUpdateService.updateUserTargets(
            targetActivity,
            'call',
            performedByUserId,
          );
        }

        return { activity: newActivity, followUp: createdFollowUp };
      });

      // Fetch the created activity with all relationships
      const createdActivity = await this.prisma.activity.findUnique({
        where: { id: activity.activity.id },
        include: {
          purpose: {
            select: {
              id: true,
              name: true,
            },
          },
          performed_by: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          attachments: {
            select: {
              id: true,
              file_url: true,
            },
          },
        },
      });

      if (!createdActivity) {
        throw new BadRequestException('Failed to retrieve created activity');
      }

      // Schedule follow-up reminder if follow-up was created
      if (activity.followUp && follow_up_date) {
        try {
          const followUpTime = new Date(follow_up_date);
          const reminderTime = new Date(followUpTime.getTime() - 2 * 60 * 60 * 1000); // 2 hours before

          // Only schedule if reminder time is in the future
          if (reminderTime > new Date()) {
            await this.backgroundTaskService.schedule(
              'follow-up-reminder',
              { followUpId: activity.followUp.id },
              reminderTime,
              {
                name: `Follow-up Reminder - ${lead.customer_name || 'Unknown Lead'}`,
                description: `Reminder for call with ${lead.customer_name || 'Unknown Lead'}`,
                priority: 8,
              }
            );
          }
        } catch (error) {
          // Log error but don't fail the activity creation
          console.error('Failed to schedule follow-up reminder:', error);
        }
      }

      // Transform to response DTO
      return this.transformToCallActivityResponse(createdActivity);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create call activity');
    }
  }

  /**
   * Private helper method to transform activity data to response DTO
   */
  private transformToCallActivityResponse(
    activity: any,
  ): CallActivityResponseDto {
    return {
      id: activity.id,
      leadId: activity.lead_id,
      activityType: activity.activity_type,
      interactionType: activity.interaction_type,
      callStatus: activity.call_status,
      notes: activity.notes,
      nextFollowupDate: activity.next_followup_date?.toISOString() || undefined,
      purposeId: activity.purpose_id,
      purpose: {
        id: activity.purpose.id,
        name: activity.purpose.name,
      },
      performedByUserId: activity.performed_by_user_id,
      performedBy: {
        id: activity.performed_by.id,
        name: activity.performed_by.name,
        email: activity.performed_by.email,
      },
      attachments: activity.attachments.map((attachment: any) => ({
        id: attachment.id,
        fileUrl: attachment.file_url,
      })),
      createdAt: activity.created_at.toISOString(),
      updatedAt: activity.updated_at.toISOString(),
    };
  }

  /**
   * Creates a new visit activity with attachments
   * @param createVisitActivityDto - Data for creating the visit activity
   * @returns Promise<VisitActivityResponseDto> - The created visit activity
   */
  async createVisitActivity(
    createVisitActivityDto: CreateVisitActivityDto,
  ): Promise<VisitActivityResponseDto> {
    const {
      visit_type,
      visit_status,
      notes,
      follow_up_date,
      purpose_id,
      leadID,
      performed_by_user_id,
      attachments = [],
    } = createVisitActivityDto;

    // Use the provided leadID directly
    const leadId = leadID;
    let performedByUserId = performed_by_user_id;

    if (!performedByUserId) {
      const firstUser = await this.prisma.user.findFirst();
      if (!firstUser) {
        throw new BadRequestException(
          'No users found in the system. Please provide a performed_by_user_id.',
        );
      }
      performedByUserId = firstUser.id;
    }

    // Validate that the lead exists
    const lead = await this.prisma.lead.findUnique({
      where: { id: leadId },
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${leadId}' not found`);
    }

    // Validate that the purpose exists
    const purpose = await this.prisma.purposeOfActivity.findUnique({
      where: { id: purpose_id },
    });

    if (!purpose) {
      throw new NotFoundException(`Purpose with ID '${purpose_id}' not found`);
    }

    // Validate that the user exists
    const user = await this.prisma.user.findUnique({
      where: { id: performedByUserId },
    });

    if (!user) {
      throw new NotFoundException(
        `User with ID '${performedByUserId}' not found`,
      );
    }

    try {
      // Create the activity with attachments in a transaction
      const activity = await this.prisma.$transaction(async (tx) => {
        // Create the activity
        const newActivity = await tx.activity.create({
          data: {
            lead_id: leadId,
            activity_type: visit_type, // Store visit_type in activity_type field
            interaction_type: 'visit', // Set interaction_type to 'visit'
            visit_status: visit_status, // Store in visit_status field
            notes: notes || null,
            next_followup_date: follow_up_date
              ? new Date(follow_up_date)
              : null,
            purpose_id: purpose_id,
            performed_by_user_id: performedByUserId,
            branch_id: user.branch_id, // Record the branch
          },
        });

        // Create attachments if provided
        if (attachments && attachments.length > 0) {
          // Save files to disk and create attachment records
          const savedFiles = await this.fileStorageService.saveFiles(
            attachments.map((att) => att.file),
            `activities/${newActivity.id}`,
          );

          // Create attachment records with actual file URLs
          await tx.activityAttachment.createMany({
            data: savedFiles.map((savedFile) => ({
              activity_id: newActivity.id,
              file_url: savedFile.url,
            })),
          });
        }

        // Create follow-up if follow_up_date is provided
        let createdFollowUp: any = null;
        if (follow_up_date) {
          createdFollowUp = await tx.followUp.create({
            data: {
              parent_activity_id: newActivity.id,
              branch_id: user.branch_id,
              user_id: performedByUserId,
              for_date: new Date(follow_up_date),
              lead_id: leadId,
              type: 'visit', // Use the interaction_type from the activity
            },
          });
        }

        // UPDATE THE USER TARGETS IF VISIT WAS SUCCESSFUL
        let targetActivity: string = 'LEADS';

        await this.targetUpdateService.updateUserTargets(
          targetActivity,
          'visit',
          performedByUserId,
        );

        return { activity: newActivity, followUp: createdFollowUp };
      });

      // Fetch the created activity with all relationships
      const createdActivity = await this.prisma.activity.findUnique({
        where: { id: activity.activity.id },
        include: {
          purpose: {
            select: {
              id: true,
              name: true,
            },
          },
          performed_by: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          attachments: {
            select: {
              id: true,
              file_url: true,
            },
          },
        },
      });

      if (!createdActivity) {
        throw new BadRequestException('Failed to retrieve created activity');
      }

      // Schedule follow-up reminder if follow-up was created
      if (activity.followUp && follow_up_date) {
        try {
          const followUpTime = new Date(follow_up_date);
          const reminderTime = new Date(followUpTime.getTime() - 2 * 60 * 60 * 1000); // 2 hours before

          // Only schedule if reminder time is in the future
          if (reminderTime > new Date()) {
            await this.backgroundTaskService.schedule(
              'follow-up-reminder',
              { followUpId: activity.followUp.id },
              reminderTime,
              {
                name: `Follow-up Reminder - ${lead.customer_name || 'Unknown Lead'}`,
                description: `Reminder for visit with ${lead.customer_name || 'Unknown Lead'}`,
                priority: 8,
              }
            );
          }
        } catch (error) {
          // Log error but don't fail the activity creation
          console.error('Failed to schedule follow-up reminder:', error);
        }
      }

      // Transform to response DTO
      return this.transformToVisitActivityResponse(createdActivity);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create visit activity');
    }
  }

  /**
   * Private helper method to transform visit activity data to response DTO
   */
  private transformToVisitActivityResponse(
    activity: any,
  ): VisitActivityResponseDto {
    return {
      id: activity.id,
      leadId: activity.lead_id,
      activityType: activity.activity_type,
      interactionType: activity.interaction_type,
      visitStatus: activity.visit_status,
      notes: activity.notes,
      nextFollowupDate: activity.next_followup_date?.toISOString() || undefined,
      purposeId: activity.purpose_id,
      purpose: {
        id: activity.purpose.id,
        name: activity.purpose.name,
      },
      performedByUserId: activity.performed_by_user_id,
      performedBy: {
        id: activity.performed_by.id,
        name: activity.performed_by.name,
        email: activity.performed_by.email,
      },
      attachments: activity.attachments.map((attachment: any) => ({
        id: attachment.id,
        fileUrl: attachment.file_url,
      })),
      createdAt: activity.created_at.toISOString(),
      updatedAt: activity.updated_at.toISOString(),
    };
  }

  /**
   * Gets interaction history for a specific lead
   * @param leadId - UUID of the lead
   * @returns Promise<InteractionHistoryDto[]> - Array of interaction history
   */
  async getLeadInteractionHistory(leadId: string) {
    // Validate that the lead exists
    const lead = await this.prisma.lead.findUnique({
      where: { id: leadId },
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID '${leadId}' not found`);
    }

    // Fetch all activities for the lead
    const activities = await this.prisma.activity.findMany({
      where: { lead_id: leadId },
      include: {
        performed_by: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        created_at: 'desc', // Most recent first
      },
    });

    // Transform to the required format
    return activities.map((activity) => ({
      interaction_type: activity.interaction_type || 'unknown',
      activity_type: activity.activity_type,
      next_followup_date: activity.next_followup_date?.toISOString() || null,
      notes: activity.notes,
      performed_by: activity.performed_by.name,
      created_at: activity.created_at.toISOString(),
    }));
  }

  /**
   * Gets all activities filtered by interaction type with RBAC implementation
   * @param interactionType - The interaction type to filter by ('call' or 'visit')
   * @param user - Optional user object with permissions for RBAC filtering
   * @returns Promise with activities data, total count, and metadata
   */
  async getActivitiesByInteractionType(
    interactionType: string,
    user?: {
      id: string;
      branch_id: string;
      permissions: string[];
    },
  ) {
    // Validate interaction type
    if (
      !interactionType ||
      !['call', 'visit'].includes(interactionType.toLowerCase())
    ) {
      throw new BadRequestException(
        'Invalid interaction type. Must be "call" or "visit"',
      );
    }

    const normalizedType = interactionType.toLowerCase();

    // Build base where clause
    const whereClause: any = {
      interaction_type: normalizedType,
    };

    // Apply RBAC filtering based on user permissions
    if (user && user.permissions) {
      const canViewAllCalls = user.permissions.includes('view.all.calls');
      const canViewAllVisits = user.permissions.includes('view.all.visits');
      const canViewBranchCalls = user.permissions.includes('view.branch.calls');
      const canViewBranchVisits =
        user.permissions.includes('view.branch.visits');
      const canViewMyCalls = user.permissions.includes('view.my.calls');
      const canViewMyVisits = user.permissions.includes('view.my.visits');
      const canViewRegionCalls = user.permissions.includes('view.region.calls');
      const canViewRegionVisits =
        user.permissions.includes('view.region.visits');

      // Check if user has any relevant permissions
      const hasRelevantPermissions =
        (normalizedType === 'call' &&
          (canViewAllCalls ||
            canViewRegionCalls ||
            canViewBranchCalls ||
            canViewMyCalls)) ||
        (normalizedType === 'visit' &&
          (canViewAllVisits ||
            canViewRegionVisits ||
            canViewBranchVisits ||
            canViewMyVisits));

      if (!hasRelevantPermissions) {
        throw new BadRequestException(
          `Insufficient permissions to view ${normalizedType} activities.`,
        );
      }

      // Apply permission-based filtering
      if (normalizedType === 'call') {
        if (canViewAllCalls) {
          // User can see all calls - no additional filtering needed
        } else if (canViewRegionCalls) {
          // User can see calls in their region
          const userBranch = await this.prisma.branch.findUnique({
            where: { id: user.branch_id },
            select: { region_id: true },
          });
          if (!userBranch?.region_id) {
            throw new BadRequestException(
              'User branch has no region configured',
            );
          }
          whereClause.branch = { region_id: userBranch.region_id };
        } else if (canViewBranchCalls) {
          // User can see calls in their branch
          whereClause.branch_id = user.branch_id;
        } else if (canViewMyCalls) {
          // User can only see their own calls
          whereClause.performed_by_user_id = user.id;
        }
      } else if (normalizedType === 'visit') {
        if (canViewAllVisits) {
          // User can see all visits - no additional filtering needed
        } else if (canViewRegionVisits) {
          // User can see visits in their region
          const userBranch = await this.prisma.branch.findUnique({
            where: { id: user.branch_id },
            select: { region_id: true },
          });
          if (!userBranch?.region_id) {
            throw new BadRequestException(
              'User branch has no region configured',
            );
          }
          whereClause.branch = { region_id: userBranch.region_id };
        } else if (canViewBranchVisits) {
          // User can see visits in their branch
          whereClause.branch_id = user.branch_id;
        } else if (canViewMyVisits) {
          // User can only see their own visits
          whereClause.performed_by_user_id = user.id;
        }
      }
    }

    try {
      // Get activities filtered by interaction_type and RBAC with all related data
      const [activities, total] = await Promise.all([
        this.prisma.activity.findMany({
          where: whereClause,
          include: {
            lead: {
              select: {
                id: true,
                customer_name: true,
                phone_number: true,
              },
            },
            performed_by: {
              select: {
                id: true,
                name: true,
                email: true,
                rm_code: true,
              },
            },
            purpose: {
              select: {
                id: true,
                name: true,
                description: true,
              },
            },
            branch: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: {
            created_at: 'desc',
          },
        }),
        this.prisma.activity.count({
          where: whereClause,
        }),
      ]);

      // Format the activities data
      const formattedActivities = activities.map((activity) => ({
        id: activity.id,
        lead_id: activity.lead_id,
        lead_name: activity.lead?.customer_name || 'Unknown Lead',
        lead_phone: activity.lead?.phone_number || null,
        interaction_type: activity.interaction_type,
        activity_type: activity.activity_type,
        call_status: activity.call_status || null,
        visit_status: activity.visit_status || null,
        notes: activity.notes || null,
        call_duration_minutes: activity.call_duration_minutes || null,
        next_followup_date: activity.next_followup_date?.toISOString() || null,
        performed_by: {
          id: activity.performed_by.id,
          name: activity.performed_by.name,
          email: activity.performed_by.email,
          rm_code: activity.performed_by.rm_code,
        },
        purpose: activity.purpose
          ? {
              id: activity.purpose.id,
              name: activity.purpose.name,
              description: activity.purpose.description,
            }
          : null,
        branch: activity.branch
          ? {
              id: activity.branch.id,
              name: activity.branch.name,
            }
          : null,
        created_at: activity.created_at.toISOString(),
        updated_at: activity.updated_at.toISOString(),
      }));

      return {
        data: formattedActivities,
        total,
        interaction_type: normalizedType,
        message: `Retrieved ${total} ${normalizedType} activities successfully`,
        user_permissions: user
          ? {
              can_view_all:
                normalizedType === 'call'
                  ? user.permissions.includes('view.all.calls')
                  : user.permissions.includes('view.all.visits'),
              can_view_branch:
                normalizedType === 'call'
                  ? user.permissions.includes('view.branch.calls')
                  : user.permissions.includes('view.branch.visits'),
              can_view_my:
                normalizedType === 'call'
                  ? user.permissions.includes('view.my.calls')
                  : user.permissions.includes('view.my.visits'),
              applied_filter: this.getAppliedFilterText(
                normalizedType,
                user.permissions,
              ),
            }
          : null,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to retrieve ${normalizedType} activities: ${error.message}`,
      );
    }
  }

  /**
   * Helper method to get the applied filter text for user permissions
   */
  private getAppliedFilterText(
    interactionType: string,
    permissions: string[],
  ): string {
    if (interactionType === 'call') {
      if (permissions.includes('view.all.calls')) return 'all_calls';
      if (permissions.includes('view.region.calls')) return 'region_calls_only';
      if (permissions.includes('view.branch.calls')) return 'branch_calls_only';
      if (permissions.includes('view.my.calls')) return 'my_calls_only';
    } else if (interactionType === 'visit') {
      if (permissions.includes('view.all.visits')) return 'all_visits';
      if (permissions.includes('view.region.visits'))
        return 'region_visits_only';
      if (permissions.includes('view.branch.visits'))
        return 'branch_visits_only';
      if (permissions.includes('view.my.visits')) return 'my_visits_only';
    }
    return 'unknown_filter';
  }

  /**
   * Gets all follow-ups with comprehensive information
   * @returns Promise<FollowupsListResponseDto> - List of all follow-ups with details, sorted by creation time (most recent first)
   */
  async getAllFollowups(): Promise<FollowupsListResponseDto> {
    try {
      // Get all activities that have a next_followup_date set
      const [followups, total] = await Promise.all([
        this.prisma.activity.findMany({
          where: {
            next_followup_date: {
              not: null,
            },
          },
          include: {
            lead: {
              select: {
                id: true,
                customer_name: true,
                rm_user_id: true,
                anchor: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
            performed_by: {
              select: {
                id: true,
                name: true,
                rm_code: true,
              },
            },
            purpose: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: {
            created_at: 'desc', // Most recently created follow-ups first
          },
        }),
        this.prisma.activity.count({
          where: {
            next_followup_date: {
              not: null,
            },
          },
        }),
      ]);

      // Get RM user information for leads that have rm_user_id
      const rmUserIds = followups
        .map((f) => f.lead?.rm_user_id)
        .filter(Boolean) as string[];

      const rmUsers = await this.prisma.user.findMany({
        where: {
          id: {
            in: rmUserIds,
          },
        },
        select: {
          id: true,
          name: true,
          rm_code: true,
        },
      });

      // Create a map for quick RM user lookup
      const rmUserMap = new Map(rmUsers.map((user) => [user.id, user]));

      // Transform to response format
      const formattedFollowups: FollowupResponseDto[] = followups.map(
        (activity) => {
          // Get assigned officer (RM user from lead or performed_by user as fallback)
          const rmUser = activity.lead?.rm_user_id
            ? rmUserMap.get(activity.lead.rm_user_id)
            : null;

          const assignedOfficer = rmUser || activity.performed_by;

          return {
            id: activity.id,
            lead_id: activity.lead_id,
            lead_name: activity.lead?.customer_name || 'Unknown Lead',
            anchor_name: activity.lead?.anchor?.name || 'No Anchor',
            assigned_officer: {
              id: assignedOfficer.id,
              name: assignedOfficer.name,
              rm_code: assignedOfficer.rm_code,
            },
            followup_type: activity.interaction_type || 'call',
            followup_reason: activity.activity_type,
            followup_date: activity.next_followup_date!.toISOString(),
            status: activity.followup_status || 'pending',
            created_date: activity.created_at.toISOString(),
            notes: activity.notes || undefined,
            purpose: activity.purpose
              ? {
                  id: activity.purpose.id,
                  name: activity.purpose.name || 'Unknown Purpose',
                }
              : undefined,
          };
        },
      );

      // Calculate summary statistics
      const summary = {
        pending: formattedFollowups.filter((f) => f.status === 'pending')
          .length,
        completed: formattedFollowups.filter((f) => f.status === 'completed')
          .length,
        cancelled: formattedFollowups.filter((f) => f.status === 'cancelled')
          .length,
        call_followups: formattedFollowups.filter(
          (f) => f.followup_type === 'call',
        ).length,
        visit_followups: formattedFollowups.filter(
          (f) => f.followup_type === 'visit',
        ).length,
      };

      return {
        data: formattedFollowups,
        total,
        summary,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to retrieve follow-ups: ${error.message}`,
      );
    }
  }

  /**
   * Updates the status of a follow-up
   * @param followupId - UUID of the follow-up (activity) to update
   * @param updateDto - New status data
   * @returns Promise<UpdateFollowupStatusResponseDto> - Updated follow-up information
   */
  async updateFollowupStatus(
    followupId: string,
    updateDto: UpdateFollowupStatusDto,
  ): Promise<UpdateFollowupStatusResponseDto> {
    try {
      // Verify the activity exists and has a follow-up date
      const activity = await this.prisma.activity.findUnique({
        where: { id: followupId },
        select: {
          id: true,
          next_followup_date: true,
          followup_status: true,
        },
      });

      if (!activity) {
        throw new NotFoundException(
          `Follow-up with ID '${followupId}' not found`,
        );
      }

      if (!activity.next_followup_date) {
        throw new BadRequestException(
          `Activity with ID '${followupId}' is not a follow-up activity`,
        );
      }

      // Update the follow-up status
      const updatedActivity = await this.prisma.activity.update({
        where: { id: followupId },
        data: {
          followup_status: updateDto.status,
        },
        select: {
          id: true,
          followup_status: true,
          updated_at: true,
        },
      });

      return {
        id: updatedActivity.id,
        status: updatedActivity.followup_status || 'pending',
        updated_at: updatedActivity.updated_at.toISOString(),
        message: 'Follow-up status updated successfully',
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update follow-up status: ${error.message}`,
      );
    }
  }
}
