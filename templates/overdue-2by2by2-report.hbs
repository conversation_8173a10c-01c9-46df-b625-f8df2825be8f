<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Overdue 2by2by2 Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #dc3545;
        }
        .header h1 {
            color: #dc3545;
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 16px;
        }
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #333;
        }
        .summary-box {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .summary-box h2 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .summary-box p {
            margin: 5px 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            margin: 25px 0;
            line-height: 1.8;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .highlight strong {
            color: #856404;
        }
        .attachment-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .attachment-info h3 {
            margin: 0 0 10px 0;
            color: #0066cc;
            font-size: 16px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 14px;
            color: #666;
            text-align: center;
        }
        .urgent-notice {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: 500;
        }
        .action-required {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
        }
        .action-required h3 {
            margin: 0 0 10px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 Overdue 2by2by2 Activities Report</h1>
            <p>{{reportDate}} • {{reportScope}}</p>
        </div>

        <div class="greeting">
            Hello {{userName}},
        </div>

        <div class="urgent-notice">
            <strong>⚠️ Urgent Action Required:</strong> There are overdue 2by2by2 activities that need immediate attention.
        </div>

        <div class="summary-box">
            <h2>{{overdueCount}}</h2>
            <p>Overdue 2by2by2 Activities</p>
            <p>Scope: {{reportScope}}</p>
        </div>

        <div class="content">
            <p>This report contains details of all 2by2by2 activities that are past their execution dates and remain incomplete.</p>
            
            <div class="action-required">
                <h3>📋 What's Included:</h3>
                <ul>
                    <li><strong>Customer Information:</strong> Names and account details</li>
                    <li><strong>Phase Details:</strong> Which phase (First 2, Second 2, Third 2) is overdue</li>
                    <li><strong>Assigned Agents:</strong> Responsible team members</li>
                    <li><strong>Overdue Duration:</strong> Days and hours past due</li>
                </ul>
            </div>

            <div class="highlight">
                <strong>📈 Impact:</strong> Overdue 2by2by2 activities can affect customer satisfaction and service quality metrics. Please review and take appropriate action promptly.
            </div>
        </div>

        <div class="attachment-info">
            <h3>📎 Detailed Report Attached</h3>
            <p>The complete overdue 2by2by2 activities report is attached as a PDF file. This includes:</p>
            <ul>
                <li>Complete list of overdue activities</li>
                <li>Customer and agent details</li>
                <li>Overdue duration breakdown</li>
                <li>Phase-wise categorization</li>
            </ul>
        </div>

        <div class="content">
            <p><strong>Next Steps:</strong></p>
            <ol>
                <li>Review the attached detailed report</li>
                <li>Contact the assigned agents to understand delays</li>
                <li>Prioritize activities based on overdue duration</li>
                <li>Update activity status once completed</li>
            </ol>
        </div>

        <div class="footer">
            <p>This is an automated report generated by the KB Tracker System.</p>
            <p>Report generated on {{reportDate}}</p>
            <p>If you have questions about this report, please contact your system administrator.</p>
        </div>
    </div>
</body>
</html>
