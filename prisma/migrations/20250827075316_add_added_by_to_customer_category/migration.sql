/*
  Warnings:

  - You are about to drop the column `activity_date` on the `loan_activities` table. All the data in the column will be lost.
  - You are about to drop the column `attachment` on the `loan_activities` table. All the data in the column will be lost.
  - You are about to drop the column `purpose` on the `loan_activities` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[email]` on the table `users` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `updated_at` to the `activity_attachments` table without a default value. This is not possible if the table is not empty.
  - Added the required column `added_by` to the `customer_categories` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `loan_activities` table without a default value. This is not possible if the table is not empty.
  - Added the required column `branch_id` to the `targets` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "TargetStatus" AS ENUM ('Active', 'Expired', 'Archived');

-- CreateEnum
CREATE TYPE "ActivityType" AS ENUM ('LEADS', 'CUSTOMER_SERVICE', 'LOAN_ACTIVITIES');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "TwoByTwoPhaseType" AS ENUM ('first2', 'second2', 'third2');

-- CreateEnum
CREATE TYPE "MfaType" AS ENUM ('EMAIL', 'SMS');

-- DropForeignKey
ALTER TABLE "activities" DROP CONSTRAINT "activities_lead_id_fkey";

-- AlterTable
ALTER TABLE "activities" ADD COLUMN     "customer_feedback_id" TEXT,
ADD COLUMN     "customer_service_hitlist_record_id" TEXT,
ADD COLUMN     "followup_status" TEXT DEFAULT 'pending',
ADD COLUMN     "two_by_two_phase_id" TEXT,
ALTER COLUMN "lead_id" DROP NOT NULL;

-- AlterTable
ALTER TABLE "activity_attachments" ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "loan_activity_id" TEXT,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "customer_categories" ADD COLUMN     "added_by" TEXT NOT NULL,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updated_at" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "leads" ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "loan_activities" DROP COLUMN "activity_date",
DROP COLUMN "attachment",
DROP COLUMN "purpose",
ADD COLUMN     "call_duration_minutes" INTEGER,
ADD COLUMN     "call_status" TEXT,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "followup_status" TEXT DEFAULT 'pending',
ADD COLUMN     "interaction_type" TEXT,
ADD COLUMN     "loan_client_id" TEXT,
ADD COLUMN     "next_followup_date" TIMESTAMP(3),
ADD COLUMN     "purpose_id" TEXT,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "visit_status" TEXT;

-- AlterTable
ALTER TABLE "targets" ADD COLUMN     "activity" "ActivityType" NOT NULL DEFAULT 'LEADS_HITLIST',
ADD COLUMN     "archived_at" TIMESTAMP(3),
ADD COLUMN     "branch_id" TEXT NOT NULL,
ADD COLUMN     "status" "TargetStatus" NOT NULL DEFAULT 'Active',
ALTER COLUMN "end_date" DROP NOT NULL;

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "last_password_update" TIMESTAMP(3);

-- CreateTable
CREATE TABLE "target_progress" (
    "id" TEXT NOT NULL,
    "target_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "period_start" TIMESTAMP(3) NOT NULL,
    "period_end" TIMESTAMP(3) NOT NULL,
    "achieved_count" INTEGER NOT NULL DEFAULT 0,
    "is_achieved" BOOLEAN,
    "is_applicable" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "target_progress_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "holidays" (
    "id" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "holidays_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_service_hitlists" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "uploaded_by" TEXT NOT NULL,
    "uploaded_date" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "customer_service_hitlists_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_service_hitlist_records" (
    "id" TEXT NOT NULL,
    "customer_service_hitlist_id" TEXT NOT NULL,
    "customer_name" TEXT NOT NULL,
    "account_number" TEXT NOT NULL,
    "phone_number" TEXT NOT NULL,
    "rm_code" TEXT NOT NULL,
    "branch_code" TEXT NOT NULL,

    CONSTRAINT "customer_service_hitlist_records_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "two_by_two_phases" (
    "id" TEXT NOT NULL,
    "type" "TwoByTwoPhaseType" NOT NULL,
    "customer_service_hitlist_record_id" TEXT NOT NULL,
    "assigned_to_user_id" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "execution_date" TIMESTAMP(3),
    "expected_completion_date" TIMESTAMP(3),
    "is_current" BOOLEAN NOT NULL DEFAULT false,
    "is_completed" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "two_by_two_phases_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "refresh_tokens" (
    "id" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "expires_at" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "refresh_tokens_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_feedback_categories" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "added_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),

    CONSTRAINT "customer_feedback_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "loan_clients" (
    "id" TEXT NOT NULL,
    "customer_name" TEXT,
    "anchor_id" TEXT,
    "customer_category_id" TEXT,
    "isic_sector_id" TEXT,
    "phone_number" TEXT,
    "type_of_lead" TEXT,
    "branch_id" TEXT,
    "rm_user_id" TEXT,
    "assigned_user" TEXT,
    "account_number" TEXT,
    "account_number_assigned_at" TIMESTAMP(3),
    "employer_id" TEXT,
    "anchor_relationship_id" TEXT,
    "lead_status" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "loan_clients_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "loan_client_contact_persons" (
    "id" TEXT NOT NULL,
    "loan_client_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "phone_number" TEXT NOT NULL,

    CONSTRAINT "loan_client_contact_persons_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "otps" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "purpose" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expires_at" TIMESTAMP(3) NOT NULL,
    "used" BOOLEAN NOT NULL DEFAULT false,
    "user_id" TEXT,
    "user_mfa_method_id" TEXT,

    CONSTRAINT "otps_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "mfa_methods" (
    "id" TEXT NOT NULL,
    "method" "MfaType" NOT NULL,
    "description" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "mfa_methods_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_mfa_methods" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "method_id" TEXT NOT NULL,
    "contact" TEXT NOT NULL,
    "enabled" BOOLEAN NOT NULL DEFAULT false,
    "verified" BOOLEAN NOT NULL DEFAULT false,
    "added_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_used_at" TIMESTAMP(3),

    CONSTRAINT "user_mfa_methods_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "target_progress_target_id_user_id_period_start_key" ON "target_progress"("target_id", "user_id", "period_start");

-- CreateIndex
CREATE UNIQUE INDEX "holidays_date_key" ON "holidays"("date");

-- CreateIndex
CREATE UNIQUE INDEX "refresh_tokens_token_key" ON "refresh_tokens"("token");

-- CreateIndex
CREATE UNIQUE INDEX "loan_clients_account_number_key" ON "loan_clients"("account_number");

-- CreateIndex
CREATE UNIQUE INDEX "user_mfa_methods_user_id_method_id_key" ON "user_mfa_methods"("user_id", "method_id");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- AddForeignKey
ALTER TABLE "customer_categories" ADD CONSTRAINT "customer_categories_added_by_fkey" FOREIGN KEY ("added_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activity_attachments" ADD CONSTRAINT "activity_attachments_loan_activity_id_fkey" FOREIGN KEY ("loan_activity_id") REFERENCES "loan_activities"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "loan_activities" ADD CONSTRAINT "loan_activities_loan_client_id_fkey" FOREIGN KEY ("loan_client_id") REFERENCES "loan_clients"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "loan_activities" ADD CONSTRAINT "loan_activities_purpose_id_fkey" FOREIGN KEY ("purpose_id") REFERENCES "purpose_of_activities"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "targets" ADD CONSTRAINT "targets_branch_id_fkey" FOREIGN KEY ("branch_id") REFERENCES "branches"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "target_progress" ADD CONSTRAINT "target_progress_target_id_fkey" FOREIGN KEY ("target_id") REFERENCES "targets"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "target_progress" ADD CONSTRAINT "target_progress_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activities" ADD CONSTRAINT "activities_customer_feedback_id_fkey" FOREIGN KEY ("customer_feedback_id") REFERENCES "customer_feedback_categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activities" ADD CONSTRAINT "activities_customer_service_hitlist_record_id_fkey" FOREIGN KEY ("customer_service_hitlist_record_id") REFERENCES "customer_service_hitlist_records"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activities" ADD CONSTRAINT "activities_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "leads"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activities" ADD CONSTRAINT "activities_two_by_two_phase_id_fkey" FOREIGN KEY ("two_by_two_phase_id") REFERENCES "two_by_two_phases"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_service_hitlists" ADD CONSTRAINT "customer_service_hitlists_uploaded_by_fkey" FOREIGN KEY ("uploaded_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_service_hitlist_records" ADD CONSTRAINT "customer_service_hitlist_records_customer_service_hitlist__fkey" FOREIGN KEY ("customer_service_hitlist_id") REFERENCES "customer_service_hitlists"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "two_by_two_phases" ADD CONSTRAINT "two_by_two_phases_assigned_to_user_id_fkey" FOREIGN KEY ("assigned_to_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "two_by_two_phases" ADD CONSTRAINT "two_by_two_phases_customer_service_hitlist_record_id_fkey" FOREIGN KEY ("customer_service_hitlist_record_id") REFERENCES "customer_service_hitlist_records"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "refresh_tokens" ADD CONSTRAINT "refresh_tokens_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_feedback_categories" ADD CONSTRAINT "customer_feedback_categories_added_by_fkey" FOREIGN KEY ("added_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "loan_clients" ADD CONSTRAINT "loan_clients_anchor_id_fkey" FOREIGN KEY ("anchor_id") REFERENCES "anchors"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "loan_clients" ADD CONSTRAINT "loan_clients_anchor_relationship_id_fkey" FOREIGN KEY ("anchor_relationship_id") REFERENCES "anchor_relationships"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "loan_clients" ADD CONSTRAINT "loan_clients_assigned_user_fkey" FOREIGN KEY ("assigned_user") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "loan_clients" ADD CONSTRAINT "loan_clients_branch_id_fkey" FOREIGN KEY ("branch_id") REFERENCES "branches"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "loan_clients" ADD CONSTRAINT "loan_clients_customer_category_id_fkey" FOREIGN KEY ("customer_category_id") REFERENCES "customer_categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "loan_clients" ADD CONSTRAINT "loan_clients_employer_id_fkey" FOREIGN KEY ("employer_id") REFERENCES "employers"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "loan_clients" ADD CONSTRAINT "loan_clients_isic_sector_id_fkey" FOREIGN KEY ("isic_sector_id") REFERENCES "isic_sectors"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "loan_clients" ADD CONSTRAINT "loan_clients_rm_user_id_fkey" FOREIGN KEY ("rm_user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "loan_client_contact_persons" ADD CONSTRAINT "loan_client_contact_persons_loan_client_id_fkey" FOREIGN KEY ("loan_client_id") REFERENCES "loan_clients"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "otps" ADD CONSTRAINT "otps_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "otps" ADD CONSTRAINT "otps_user_mfa_method_id_fkey" FOREIGN KEY ("user_mfa_method_id") REFERENCES "user_mfa_methods"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_mfa_methods" ADD CONSTRAINT "user_mfa_methods_method_id_fkey" FOREIGN KEY ("method_id") REFERENCES "mfa_methods"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_mfa_methods" ADD CONSTRAINT "user_mfa_methods_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
