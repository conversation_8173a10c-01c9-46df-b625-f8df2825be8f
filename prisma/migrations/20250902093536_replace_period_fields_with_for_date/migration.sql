/*
  Warnings:

  - The values [Expired] on the enum `TargetStatus` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `period_end` on the `target_progress` table. All the data in the column will be lost.
  - You are about to drop the column `period_start` on the `target_progress` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[target_id,user_id,for_date]` on the table `target_progress` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `added_by` to the `holidays` table without a default value. This is not possible if the table is not empty.
  - Added the required column `for_date` to the `target_progress` table without a default value. This is not possible if the table is not empty.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "TargetStatus_new" AS ENUM ('Active', 'Archived');
ALTER TABLE "targets" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "targets" ALTER COLUMN "status" TYPE "TargetStatus_new" USING ("status"::text::"TargetStatus_new");
ALTER TYPE "TargetStatus" RENAME TO "TargetStatus_old";
ALTER TYPE "TargetStatus_new" RENAME TO "TargetStatus";
DROP TYPE "TargetStatus_old";
ALTER TABLE "targets" ALTER COLUMN "status" SET DEFAULT 'Active';
COMMIT;

-- DropIndex
DROP INDEX "holidays_date_key";

-- DropIndex
DROP INDEX "target_progress_target_id_user_id_period_start_key";

-- AlterTable
ALTER TABLE "activities" ADD COLUMN     "follow_up_id" TEXT;

-- AlterTable
ALTER TABLE "customer_service_hitlist_records" ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "holidays" ADD COLUMN     "added_by" TEXT NOT NULL,
ADD COLUMN     "name" TEXT;

-- AlterTable
ALTER TABLE "target_progress" DROP COLUMN "period_end",
DROP COLUMN "period_start",
ADD COLUMN     "for_date" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "target_value" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "two_by_two_phases" ADD COLUMN     "is_verified_workday" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "call_mtd_target" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "profile_photo_url" TEXT,
ADD COLUMN     "target_related_calls" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "target_related_visits" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "visit_mtd_target" INTEGER NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE "follow_ups" (
    "id" TEXT NOT NULL,
    "parent_activity_id" TEXT NOT NULL,
    "branch_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "for_date" TIMESTAMP(3) NOT NULL,
    "date_completed" TIMESTAMP(3),
    "lead_id" TEXT NOT NULL,
    "type" TEXT NOT NULL DEFAULT 'call',
    "canceled_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "follow_ups_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "support_tickets" (
    "id" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "priority" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "submitted_by" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "support_tickets_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "target_progress_target_id_user_id_for_date_key" ON "target_progress"("target_id", "user_id", "for_date");

-- AddForeignKey
ALTER TABLE "holidays" ADD CONSTRAINT "holidays_added_by_fkey" FOREIGN KEY ("added_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "activities" ADD CONSTRAINT "activities_follow_up_id_fkey" FOREIGN KEY ("follow_up_id") REFERENCES "follow_ups"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "follow_ups" ADD CONSTRAINT "follow_ups_parent_activity_id_fkey" FOREIGN KEY ("parent_activity_id") REFERENCES "activities"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "follow_ups" ADD CONSTRAINT "follow_ups_branch_id_fkey" FOREIGN KEY ("branch_id") REFERENCES "branches"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "follow_ups" ADD CONSTRAINT "follow_ups_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "follow_ups" ADD CONSTRAINT "follow_ups_lead_id_fkey" FOREIGN KEY ("lead_id") REFERENCES "leads"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "support_tickets" ADD CONSTRAINT "support_tickets_submitted_by_fkey" FOREIGN KEY ("submitted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
