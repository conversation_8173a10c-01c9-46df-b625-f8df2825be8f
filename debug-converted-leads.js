const { PrismaClient } = require('@prisma/client');
const { startOfMonth, endOfMonth, startOfWeek, endOfWeek } = require('date-fns');
const prisma = new PrismaClient();

async function debugConvertedLeads() {
  try {
    console.log('=== DEBUGGING CONVERTED LEADS DATA ===');
    
    const today = new Date();
    
    // Monthly data
    const monthStart = startOfMonth(today);
    const monthEnd = endOfMonth(today);
    
    console.log(`\nMONTHLY PERIOD: ${monthStart.toDateString()} to ${monthEnd.toDateString()}`);
    
    // Get converted leads for the month
    const monthlyConvertedLeads = await prisma.lead.findMany({
      where: {
        account_number: { not: null },
        account_number_assigned_at: {
          gte: monthStart,
          lte: monthEnd,
        },
      },
      include: {
        branch: {
          include: {
            region: true,
          },
        },
        account_number_assigned_by_user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: [{ branch: { name: 'asc' } }, { account_number_assigned_at: 'desc' }],
    });
    
    console.log(`Found ${monthlyConvertedLeads.length} converted leads this month:`);
    monthlyConvertedLeads.forEach(lead => {
      console.log(`  - ${lead.customer_name} (${lead.account_number}) - Converted: ${lead.account_number_assigned_at?.toDateString()} - Branch: ${lead.branch?.name}`);
    });
    
    // Get new leads for the month
    const monthlyNewLeads = await prisma.lead.findMany({
      where: {
        created_at: {
          gte: monthStart,
          lte: monthEnd,
        },
      },
      include: {
        branch: {
          include: {
            region: true,
          },
        },
        rm_user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: [{ branch: { name: 'asc' } }, { created_at: 'desc' }],
    });
    
    console.log(`\nFound ${monthlyNewLeads.length} new leads this month:`);
    monthlyNewLeads.forEach(lead => {
      console.log(`  - ${lead.customer_name} - Created: ${lead.created_at.toDateString()} - Branch: ${lead.branch?.name} - Converted: ${lead.account_number ? 'Yes' : 'No'}`);
    });
    
    // Weekly data
    const weekStart = startOfWeek(today, { weekStartsOn: 1 }); // Monday
    const weekEnd = endOfWeek(today, { weekStartsOn: 1 });
    
    console.log(`\nWEEKLY PERIOD: ${weekStart.toDateString()} to ${weekEnd.toDateString()}`);
    
    const weeklyConvertedLeads = await prisma.lead.findMany({
      where: {
        account_number: { not: null },
        account_number_assigned_at: {
          gte: weekStart,
          lte: weekEnd,
        },
      },
      include: {
        branch: {
          include: {
            region: true,
          },
        },
        account_number_assigned_by_user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
    
    console.log(`Found ${weeklyConvertedLeads.length} converted leads this week:`);
    weeklyConvertedLeads.forEach(lead => {
      console.log(`  - ${lead.customer_name} (${lead.account_number}) - Converted: ${lead.account_number_assigned_at?.toDateString()}`);
    });
    
    // Check users with permissions
    console.log('\n=== USERS WITH PERMISSIONS ===');
    
    const usersWithMonthlyPermissions = await prisma.user.findMany({
      where: {
        role: {
          role_permissions: {
            some: {
              permission: {
                id: {
                  in: [
                    'reports.converted.leads.all.monthly',
                    'reports.converted.leads.region.monthly',
                    'reports.converted.leads.branch.monthly',
                  ],
                },
              },
            },
          },
        },
      },
      include: {
        branch: {
          include: {
            region: true,
          },
        },
        role: {
          include: {
            role_permissions: {
              include: {
                permission: {
                  select: {
                    id: true,
                  },
                },
              },
            },
          },
        },
      },
    });
    
    console.log(`Found ${usersWithMonthlyPermissions.length} users with monthly permissions:`);
    usersWithMonthlyPermissions.forEach(user => {
      const permissions = user.role.role_permissions
        .map(rp => rp.permission.id)
        .filter(id => id.includes('converted.leads') && id.includes('monthly'));
      
      console.log(`  - ${user.name} (${user.email}) - Branch: ${user.branch?.name} - Permissions: ${permissions.join(', ')}`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugConvertedLeads();
